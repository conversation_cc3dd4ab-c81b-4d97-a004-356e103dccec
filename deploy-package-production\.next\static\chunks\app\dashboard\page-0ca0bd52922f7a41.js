(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{18466:(e,a,t)=>{"use strict";t.d(a,{DialogProvider:()=>x,Ue:()=>p,G_:()=>g});var s=t(95155),r=t(12115),l=t(81284),i=t(1243),n=t(40646),o=t(85339),d=t(54416);let c={default:{icon:l.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},danger:{icon:i.A,iconColor:"text-red-400",confirmButtonClass:"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"},success:{icon:n.A,iconColor:"text-green-400",confirmButtonClass:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"},warning:{icon:o.A,iconColor:"text-yellow-400",confirmButtonClass:"bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700"},info:{icon:l.A,iconColor:"text-blue-400",confirmButtonClass:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"}};function h(e){let{isOpen:a,title:t,message:l,confirmText:i="Confirm",cancelText:n="Cancel",type:o="default",onConfirm:h,onCancel:u,loading:x=!1}=e,m=(0,r.useRef)(null),g=(0,r.useRef)(null),p=c[o],b=p.icon;return((0,r.useEffect)(()=>{if(!a)return;let e=e=>{"Escape"===e.key?u():"Enter"!==e.key||x||h()};return g.current&&g.current.focus(),document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,u,h,x]),(0,r.useEffect)(()=>(a?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[a]),a)?(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:e=>{e.target===e.currentTarget&&u()},children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm animate-fadeIn"}),(0,s.jsxs)("div",{ref:m,className:"relative bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/10 w-full max-w-md mx-auto animate-slideInScale",role:"dialog","aria-modal":"true","aria-labelledby":"dialog-title","aria-describedby":"dialog-description",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 pb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-full bg-white/10 ".concat(p.iconColor),children:(0,s.jsx)(b,{size:20})}),(0,s.jsx)("h3",{id:"dialog-title",className:"text-xl font-semibold text-white",children:t})]}),(0,s.jsx)("button",{onClick:u,className:"text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-white/10","aria-label":"Close dialog",children:(0,s.jsx)(d.A,{size:20})})]}),(0,s.jsxs)("div",{className:"px-6 pb-6",children:[(0,s.jsx)("p",{id:"dialog-description",className:"text-gray-300 leading-relaxed mb-6",children:l}),(0,s.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row gap-3 sm:justify-end",children:[(0,s.jsx)("button",{onClick:u,disabled:x,className:"px-6 py-3 text-gray-300 hover:text-white bg-white/10 hover:bg-white/20 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:n}),(0,s.jsx)("button",{ref:g,onClick:h,disabled:x,className:"px-6 py-3 text-white font-medium rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ".concat(p.confirmButtonClass),children:x?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,s.jsx)("span",{children:"Processing..."})]}):i})]})]})]})]}):null}let u=(0,r.createContext)(void 0);function x(e){let{children:a}=e,[t,l]=(0,r.useState)({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}),i=(0,r.useCallback)(e=>new Promise(a=>{l({isOpen:!0,options:{confirmText:"Confirm",cancelText:"Cancel",type:"default",...e},resolve:a,loading:!1})}),[]),n=(0,r.useCallback)(function(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"info";return new Promise(s=>{l({isOpen:!0,options:{title:e,message:a,confirmText:"OK",cancelText:"",type:t},resolve:e=>s(),loading:!1})})},[]),o=(0,r.useCallback)(()=>{t.resolve&&(l(e=>({...e,loading:!0})),setTimeout(()=>{t.resolve(!0),l({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1})},100))},[t.resolve]),d=(0,r.useCallback)(()=>{t.resolve&&(t.resolve(!1),l({isOpen:!1,options:{title:"",message:""},resolve:null,loading:!1}))},[t.resolve]);return(0,s.jsxs)(u.Provider,{value:{showConfirmation:i,showAlert:n},children:[a,(0,s.jsx)(h,{isOpen:t.isOpen,title:t.options.title,message:t.options.message,confirmText:t.options.confirmText,cancelText:t.options.cancelText,type:t.options.type,onConfirm:o,onCancel:d,loading:t.loading})]})}function m(){let e=(0,r.useContext)(u);if(void 0===e)throw Error("useDialog must be used within a DialogProvider");return e}function g(){let{showConfirmation:e}=m();return{confirmDelete:a=>e({title:"Confirm Delete",message:a?'Are you sure you want to delete "'.concat(a,'"? This action cannot be undone.'):"Are you sure you want to delete this item? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",type:"danger"}),confirmLogout:()=>e({title:"Confirm Logout",message:"Are you sure you want to logout?",confirmText:"Logout",cancelText:"Cancel",type:"warning"}),confirmAction:function(a,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Confirm";return e({title:a,message:t,confirmText:s,cancelText:"Cancel",type:"default"})}}}function p(){let{showAlert:e}=m();return{showSuccess:(a,t)=>e(a,t,"success"),showError:(a,t)=>e(a,t,"danger"),showWarning:(a,t)=>e(a,t,"warning"),showInfo:(a,t)=>e(a,t,"info")}}},24121:(e,a,t)=>{Promise.resolve().then(t.bind(t,25999))},25999:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>X});var s=t(95155),r=t(12115),l=t(35695),i=t(59377),n=t(18466),o=t(37831),d=t(92731),c=t(69783),h=t(51154),u=function(e){return e.AUTH="AUTH",e.TRANSLATION="TRANSLATION",e.API="API",e.UI="UI",e.NAVIGATION="NAVIGATION",e.BIRTH_CHART="BIRTH_CHART",e.DAILY_GUIDE="DAILY_GUIDE",e.ADMIN="ADMIN",e.QR="QR",e.SYSTEM="SYSTEM",e}({});class x{generateSessionId(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}setupGlobalErrorHandlers(){window.addEventListener("error",e=>{var a;this.log("ERROR","SYSTEM","Unhandled Error",{message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,error:null==(a=e.error)?void 0:a.stack})}),window.addEventListener("unhandledrejection",e=>{var a;this.log("ERROR","SYSTEM","Unhandled Promise Rejection",{reason:e.reason,stack:null==(a=e.reason)?void 0:a.stack})})}log(e,a,t,s,r){let l={timestamp:new Date().toISOString(),level:e,category:a,message:t,data:s,userId:r,sessionId:this.sessionId,url:window.location.href,userAgent:window.navigator.userAgent};this.logs.push(l),this.logs.length>this.maxLogs&&this.logs.shift(),this.outputToConsole(l),("CRITICAL"===e||"ERROR"===e)&&this.sendToServer(l)}outputToConsole(e){let a="[".concat(e.timestamp,"] [").concat(e.level,"] [").concat(e.category,"]"),t=this.getConsoleStyle(e.level);e.data?(console.groupCollapsed("%c".concat(a," ").concat(e.message),t),console.log("Data:",e.data),e.userId&&console.log("User ID:",e.userId),e.url&&console.log("URL:",e.url),e.stack&&console.log("Stack:",e.stack),console.groupEnd()):console.log("%c".concat(a," ").concat(e.message),t)}getConsoleStyle(e){switch(e){case"DEBUG":return"color: #888; font-size: 11px;";case"INFO":return"color: #2196F3; font-weight: bold;";case"WARN":return"color: #FF9800; font-weight: bold;";case"ERROR":return"color: #F44336; font-weight: bold;";case"CRITICAL":return"color: #FFFFFF; background-color: #F44336; font-weight: bold; padding: 2px 4px;";default:return""}}async sendToServer(e){try{await fetch("/api/logs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(e){console.error("Failed to send log to server:",e)}}debug(e,a,t,s){this.log("DEBUG",e,a,t,s)}info(e,a,t,s){this.log("INFO",e,a,t,s)}warn(e,a,t,s){this.log("WARN",e,a,t,s)}error(e,a,t,s){this.log("ERROR",e,a,t,s)}critical(e,a,t,s){this.log("CRITICAL",e,a,t,s)}getLogs(e,a){return this.logs.filter(t=>(!e||t.level===e)&&(!a||t.category===a))}clearLogs(){this.logs=[],this.info("SYSTEM","Logs cleared")}exportLogs(){return JSON.stringify(this.logs,null,2)}constructor(){this.logs=[],this.maxLogs=1e3,this.sessionId=this.generateSessionId(),this.setupGlobalErrorHandlers(),this.log("INFO","SYSTEM","App Logger initialized",{sessionId:this.sessionId})}}let m=new x,g=(e,a,t)=>{m.info("UI","User Action: ".concat(e),a,t)},p=(e,a,t,s)=>{m.info("API","API Call: ".concat(a," ").concat(e),t,s)},b=(e,a,t,s)=>{m.log(a?"INFO":"ERROR","API","API Response: ".concat(e),t,s)},f=(e,a,t,s,r)=>{m.log(s?"DEBUG":"WARN","TRANSLATION","Translation: ".concat(a," -> ").concat(t),{text:e.substring(0,50)+"...",success:s},r)},y=(e,a,t)=>{m.error("SYSTEM","Error in ".concat(a,": ").concat(e.message),{stack:e.stack,context:a},t)},j=function(e){let{text:a,className:t="",fallback:l,showLoader:n=!0,translations:o}=e,{language:d,translate:c,isTranslating:x}=(0,i.o)(),[g,p]=(0,r.useState)(a),[b,y]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{(async()=>{var e,t,s;let r=a.substring(0,50)+(a.length>50?"...":"");if(m.debug(u.TRANSLATION,"TranslatedText processing",{textPreview:r,language:d,hasTranslations:!!o,translationsStructure:o?{keys:Object.keys(o),hasEn:!!o.en,hasSi:!!o.si,enValue:(null==(e=o.en)?void 0:e.substring(0,50))+(o.en&&o.en.length>50?"...":""),siValue:(null==(t=o.si)?void 0:t.substring(0,50))+(o.si&&o.si.length>50?"...":"")}:null,hasLanguageTranslation:!!(o&&o[d]),fallbackAvailable:!!l}),o&&o[d]){m.info(u.TRANSLATION,"Using pre-translated content",{textPreview:r,language:d,translatedValue:(null==(s=o[d])?void 0:s.substring(0,50))+"..."}),p(o[d]),y(!1);return}if("en"===d){m.debug(u.TRANSLATION,"Using original English text",{textPreview:r}),p(a),y(!1);return}m.warn(u.TRANSLATION,"No pre-translated content, using API",{textPreview:r,language:d,translationsReceived:o,reasonForAPI:o?o[d]?"Unknown":"No translation for language":"No translations object"}),y(!0);try{let e=await c(a);f(a,"en",d,!0),p(e)}catch(e){f(a,"en",d,!1),m.error(u.TRANSLATION,"Translation API failed",{textPreview:r,language:d,error:e.message,fallbackUsed:!!l}),p(l||a)}finally{y(!1)}})()},[a,d,c,l,o]),b&&n)?(0,s.jsxs)("span",{className:"inline-flex items-center gap-2 ".concat(t),children:[(0,s.jsx)(h.A,{className:"w-4 h-4 animate-spin"}),(0,s.jsx)("span",{className:"opacity-70",children:l||a})]}):(0,s.jsx)("span",{className:t,children:g})};var v=t(28727),N=t(91340),w=t(34835);function k(e){let{activeTab:a,onTabChange:t,onLogout:r,className:l=""}=e;return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"md:hidden ".concat(l),children:(0,s.jsx)("button",{onClick:()=>{null==r||r()},className:"flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors",children:(0,s.jsx)(w.A,{size:20,className:"text-white"})})})})}var S=t(85339),A=t(69074),C=t(14186),_=t(4516),R=t(47863),T=t(66474);t(4612);let D={Sun:"Su",Moon:"Mo",Mars:"Ma",Mercury:"Me",Jupiter:"Ju",Venus:"Ve",Saturn:"Sa",Rahu:"Ra",Ketu:"Ke",Uranus:"Ur",Neptune:"Ne",Pluto:"Pl"};function P(e,a,t){if(!a)return D[e]||e.substring(0,2);let s={Sun:"සූ",Moon:"චන්",Mars:"අඟ",Mercury:"බු",Jupiter:"ගු",Venus:"ශු",Saturn:"සෙ",Rahu:"රා",Ketu:"කේ",Uranus:"යු",Neptune:"නෙ",Pluto:"ප්ලූ",Surya:"සූ",Chandra:"චන්",Mangal:"අඟ",Budha:"බු",Guru:"ගු",Shukra:"ශු",Shani:"සෙ",Chiron:"චි",chiron:"චි",Sirius:"සි",sirius:"සි",sun:"සූ",moon:"චන්",mars:"අඟ",mercury:"බු",jupiter:"ගු",venus:"ශු",saturn:"සෙ",rahu:"රා",ketu:"කේ",uranus:"යු",neptune:"නෙ",pluto:"ප්ලූ"};if("si"===t&&s[e])return console.log("\uD83D\uDD24 Direct Sinhala planet symbol:",{originalPlanet:e,translatedSymbol:s[e],language:t}),s[e];let r={Sun:a("sun_symbol"),Moon:a("moon_symbol"),Mars:a("mars_symbol"),Mercury:a("mercury_symbol"),Jupiter:a("jupiter_symbol"),Venus:a("venus_symbol"),Saturn:a("saturn_symbol"),Rahu:a("rahu_symbol"),Ketu:a("ketu_symbol"),Uranus:a("uranus_symbol"),Neptune:a("neptune_symbol"),Pluto:a("pluto_symbol"),Surya:a("sun_symbol"),Chandra:a("moon_symbol"),Mangal:a("mars_symbol"),Budha:a("mercury_symbol"),Guru:a("jupiter_symbol"),Shukra:a("venus_symbol"),Shani:a("saturn_symbol")};return console.log("\uD83D\uDD24 Planet Symbol Translation:",{originalPlanet:e,translatedSymbol:r[e]||D[e]||e.substring(0,2),foundInSymbolMap:!!r[e],foundInPlanetSymbols:!!D[e],language:t}),r[e]||D[e]||e.substring(0,2)}function L(e,a,t){return e&&e.houses?{...e,houses:e.houses.map(e=>({...e,planets:e.planets.map(e=>({...e,symbol:P(e.name,a,t)}))}))}:e}let I={sun:"Surya",moon:"Chandra",mercury:"Budha",venus:"Shukra",mars:"Mangal",jupiter:"Guru",saturn:"Shani",rahu:"Rahu",ketu:"Ketu"};function E(e){return null[Math.floor(e/30)]||"Unknown"}function M(e){let a=e;for(;a<0;)a+=360;for(;a>=360;)a-=360;let t=Math.floor(a/30)+1;return t>12?t-12:t}function U(e){return null[Math.floor(e/(360/27))]||"Unknown"}var O=t(35084);function G(e){var a,t,l;let{chartData:n,title:o,className:d=""}=e,{t:c}=(0,O.LL)(),{language:h}=(0,i.o)(),u=r.useMemo(()=>Math.random().toString(36).substr(2,9),[]);console.log("\uD83D\uDD0D VedicChart component received data:",{title:o,chartData:n,hasHouses:(null==n||null==(a=n.houses)?void 0:a.length)>0,housesCount:(null==n||null==(t=n.houses)?void 0:t.length)||0,firstHouse:null==n||null==(l=n.houses)?void 0:l[0],ascendantHouse:null==n?void 0:n.ascendantHouse,className:d,chartId:u,currentLanguage:h,isDesktop:window.innerWidth>=768});let x=e=>{if(!e)return e;let a={Aries:"මේෂ",Taurus:"වෘෂභ",Gemini:"මිථුන",Cancer:"කටක",Leo:"සිංහ",Virgo:"කන්‍යා",Libra:"තුලා",Scorpio:"වෘශ්චික",Sagittarius:"ධනු",Capricorn:"මකර",Aquarius:"කුම්භ",Pisces:"මීන"};if("si"===h&&a[e])return console.log("\uD83D\uDD24 VedicChart Direct Sinhala translation:",{originalSign:e,translatedSign:a[e],language:h}),a[e];let t={Aries:c("aries"),Taurus:c("taurus"),Gemini:c("gemini"),Cancer:c("cancer"),Leo:c("leo"),Virgo:c("virgo"),Libra:c("libra"),Scorpio:c("scorpio"),Sagittarius:c("sagittarius"),Capricorn:c("capricorn"),Aquarius:c("aquarius"),Pisces:c("pisces")};return console.log("\uD83D\uDD24 VedicChart Zodiac Sign Translation:",{originalSign:e,translatedSign:t[e]||e,availableTranslations:Object.keys(t),currentLanguage:h,testTranslations:{aries:c("aries"),pisces:c("pisces"),libra:c("libra")}}),t[e]||e},m=()=>[{x:50,y:50,width:100,height:100,house:12},{x:150,y:50,width:100,height:100,house:1},{x:250,y:50,width:100,height:100,house:2},{x:350,y:50,width:100,height:100,house:3},{x:350,y:150,width:100,height:100,house:4},{x:350,y:250,width:100,height:100,house:5},{x:250,y:350,width:100,height:100,house:6},{x:150,y:350,width:100,height:100,house:7},{x:50,y:350,width:100,height:100,house:8},{x:50,y:250,width:100,height:100,house:9},{x:50,y:150,width:100,height:100,house:10},{x:350,y:350,width:100,height:100,house:11}].map(e=>(0,s.jsx)("rect",{x:e.x+2,y:e.y+2,width:e.width-4,height:e.height-4,fill:"url(#houseGradient)",opacity:"0.3",rx:"8"},"bg-".concat(e.house))),g=e=>(console.log("\uD83D\uDD0D VedicChart - Rendering houses:",e),[{x:200,y:100,house:1,width:100,height:100},{x:300,y:100,house:2,width:100,height:100},{x:400,y:100,house:3,width:100,height:100},{x:400,y:200,house:4,width:100,height:100},{x:400,y:300,house:5,width:100,height:100},{x:300,y:400,house:6,width:100,height:100},{x:200,y:400,house:7,width:100,height:100},{x:100,y:400,house:8,width:100,height:100},{x:100,y:300,house:9,width:100,height:100},{x:100,y:200,house:10,width:100,height:100},{x:400,y:400,house:11,width:100,height:100},{x:100,y:100,house:12,width:100,height:100}].map((a,t)=>{let r=e.find(e=>e.houseNumber===a.house);return r?(0,s.jsxs)("g",{children:[(0,s.jsx)("text",{x:a.x,y:a.y-30,textAnchor:"middle",dominantBaseline:"middle",className:"text-lg font-bold fill-orange-300",filter:"url(#textShadow-".concat(u,")"),style:{fontSize:"18px",fontWeight:"bold"},children:a.house}),(0,s.jsx)("text",{x:a.x,y:a.y-8,textAnchor:"middle",dominantBaseline:"middle",className:"text-sm fill-blue-200 font-semibold",filter:"url(#textShadow-".concat(u,")"),style:{fontSize:"14px",fontWeight:"600"},children:x(r.sign)}),r.planets&&r.planets.map((e,t)=>{let l,i,n=r.planets.length;return 1===n?(l=a.x,i=a.y+20):2===n?(l=a.x+(0===t?-20:20),i=a.y+20):3===n?0===t?(l=a.x,i=a.y+15):(l=a.x+(1===t?-20:20),i=a.y+30):(l=a.x+(t%2==0?-20:20),i=a.y+15+15*Math.floor(t/2)),(0,s.jsx)("g",{children:(0,s.jsxs)("text",{x:l,y:i,textAnchor:"middle",dominantBaseline:"middle",className:"text-sm font-bold ".concat(p(e.name)),filter:"url(#textShadow-".concat(u,")"),style:{fontSize:"16px",fontWeight:"bold"},children:[P(e.name,c,h),e.retrograde&&(0,s.jsx)("tspan",{className:"text-red-300 text-sm font-bold",children:"ᴿ"})]})},"house-".concat(a.house,"-planet-").concat(e.name,"-").concat(t))})]},"house-".concat(a.house,"-").concat(t)):(console.log("\uD83D\uDD0D VedicChart - House ".concat(a.house," not found in data")),null)})),p=e=>({Sun:"fill-yellow-300",Moon:"fill-blue-200",Mars:"fill-red-300",Mercury:"fill-green-300",Jupiter:"fill-yellow-200",Venus:"fill-pink-300",Saturn:"fill-purple-300",Rahu:"fill-gray-300",Ketu:"fill-gray-200",Uranus:"fill-cyan-300",Neptune:"fill-blue-300",Pluto:"fill-indigo-300",Surya:"fill-yellow-300",Chandra:"fill-blue-200",Mangal:"fill-red-300",Budha:"fill-green-300",Guru:"fill-yellow-200",Shukra:"fill-pink-300",Shani:"fill-purple-300"})[e]||"fill-white";return(0,s.jsxs)("div",{className:"vedic-chart-container relative w-full bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-md rounded-3xl p-4 md:p-6 lg:p-8 border border-purple-400/30 shadow-2xl overflow-visible ".concat(d),style:{minHeight:"400px",display:"block !important",visibility:"visible !important",maxWidth:"100%",opacity:1,position:"relative",zIndex:1},children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 rounded-3xl"}),(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]"}),(0,s.jsx)("div",{className:"absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_50%)]"}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsxs)("h3",{className:"text-xl md:text-2xl lg:text-3xl font-bold text-center text-white mb-4 md:mb-6 lg:mb-8 bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 bg-clip-text text-transparent drop-shadow-lg",children:["✨ ",o," ✨"]}),(()=>{let e=(null==n?void 0:n.houses)||[];return n&&e&&0!==e.length?(0,s.jsx)("div",{className:"relative w-full max-w-[450px] md:max-w-[500px] lg:max-w-[550px] mx-auto z-10",style:{aspectRatio:"1/1",display:"block !important",visibility:"visible !important",opacity:1},children:(0,s.jsxs)("svg",{viewBox:"0 0 500 500",className:"vedic-chart-svg w-full h-full drop-shadow-2xl",style:{display:"block !important",maxWidth:"100%",height:"auto",visibility:"visible !important",opacity:1},children:[(0,s.jsxs)("defs",{children:[(0,s.jsxs)("radialGradient",{id:"chartRadialGradient-".concat(u),cx:"50%",cy:"50%",r:"50%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#1e1b4b",stopOpacity:"0.95"}),(0,s.jsx)("stop",{offset:"30%",stopColor:"#312e81",stopOpacity:"0.9"}),(0,s.jsx)("stop",{offset:"70%",stopColor:"#4c1d95",stopOpacity:"0.85"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#0f0f23",stopOpacity:"0.95"})]}),(0,s.jsxs)("linearGradient",{id:"borderGradient-".concat(u),x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#fbbf24"}),(0,s.jsx)("stop",{offset:"25%",stopColor:"#f97316"}),(0,s.jsx)("stop",{offset:"50%",stopColor:"#ea580c"}),(0,s.jsx)("stop",{offset:"75%",stopColor:"#f97316"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#fbbf24"})]}),(0,s.jsxs)("linearGradient",{id:"houseGradient-".concat(u),x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#1e1b4b",stopOpacity:"0.8"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#312e81",stopOpacity:"0.6"})]}),(0,s.jsxs)("filter",{id:"enhancedGlow-".concat(u),children:[(0,s.jsx)("feGaussianBlur",{stdDeviation:"4",result:"coloredBlur"}),(0,s.jsxs)("feMerge",{children:[(0,s.jsx)("feMergeNode",{in:"coloredBlur"}),(0,s.jsx)("feMergeNode",{in:"SourceGraphic"})]})]}),(0,s.jsx)("filter",{id:"textShadow-".concat(u),children:(0,s.jsx)("feDropShadow",{dx:"2",dy:"2",stdDeviation:"3",floodColor:"#000000",floodOpacity:"0.8"})})]}),(0,s.jsx)("rect",{x:"50",y:"50",width:"400",height:"400",fill:"url(#chartRadialGradient-".concat(u,")"),stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"5",rx:"20",filter:"url(#enhancedGlow-".concat(u,")"),style:{filter:"drop-shadow(0 0 15px rgba(251, 191, 36, 0.4))"}}),(0,s.jsx)("line",{x1:"150",y1:"50",x2:"150",y2:"450",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"250",y1:"50",x2:"250",y2:"450",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"350",y1:"50",x2:"350",y2:"450",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"50",y1:"150",x2:"450",y2:"150",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"50",y1:"250",x2:"450",y2:"250",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"50",y1:"350",x2:"450",y2:"350",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"3",opacity:"0.9"}),(0,s.jsx)("line",{x1:"150",y1:"150",x2:"350",y2:"350",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"2",opacity:"0.7"}),(0,s.jsx)("line",{x1:"350",y1:"150",x2:"150",y2:"350",stroke:"url(#borderGradient-".concat(u,")"),strokeWidth:"2",opacity:"0.7"}),m(),g(e)]})}):(console.log("\uD83D\uDD0D VedicChart - No chart data available for:",o),console.log("\uD83D\uDD0D Chart data:",n),console.log("\uD83D\uDD0D Houses:",e),(0,s.jsx)("div",{className:"relative w-full max-w-[500px] h-[500px] mx-auto flex items-center justify-center",style:{display:"block",visibility:"visible"},children:(0,s.jsxs)("div",{className:"text-center p-6 md:p-8 bg-purple-800/20 rounded-lg border border-purple-500/20",children:[(0,s.jsx)("h3",{className:"text-base md:text-lg font-semibold text-white mb-2",children:c("chart_data_not_available")}),(0,s.jsx)("p",{className:"text-purple-200 text-sm",children:c("chart_being_calculated")}),(0,s.jsxs)("div",{className:"text-xs text-purple-400 mt-2 bg-purple-900/30 rounded p-2",children:[(0,s.jsxs)("p",{children:["Chart: ",o]}),(0,s.jsxs)("p",{children:["Houses: ",(null==e?void 0:e.length)||0]})]})]})}))})(),(0,s.jsx)("div",{className:"mt-4 md:mt-6 lg:mt-8 space-y-3 md:space-y-4",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-black/30 to-black/20 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg",children:(0,s.jsxs)("div",{className:"text-sm md:text-base text-gray-100 text-center space-y-3",children:[(0,s.jsxs)("p",{className:"font-semibold text-purple-100 flex items-center justify-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83C\uDFE0"}),c("houses_numbered_1_12")]}),(0,s.jsxs)("p",{className:"hidden md:block text-blue-100 flex items-center justify-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83C\uDF1F"}),c("planet_symbols_used")]}),(0,s.jsxs)("p",{className:"text-orange-100 flex items-center justify-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDD04"}),c("r_indicates_retrograde")]})]})})})]})]})}function z(e){let a,{karakTable:t,avasthaTable:r,planetaryDetails:l,vimshottariDasha:n,ashtakavarga:o}=e,{t:d}=(0,O.LL)(),{language:c}=(0,i.o)();console.log("\uD83D\uDD0D AstrologicalTables Language Context:",{currentLanguage:c,testTranslations:{libra:d("libra"),pisces:d("pisces"),aquarius:d("aquarius"),chiron:d("chiron"),sirius:d("sirius"),susupla:d("susupla"),vadha:d("vadha")}}),console.log("\uD83D\uDD0D AstrologicalTables Data:",{planetaryDetails:null==l?void 0:l.slice(0,3),avasthaTable:r?Object.keys(r).slice(0,3):null,karakTable:t?Object.keys(t).slice(0,3):null});let h=e=>{if(!e)return e;console.log("\uD83E\uDE90 Attempting to translate planet:",e);let a={Sun:"සූර්ය",Moon:"චන්ද්‍ර",Mars:"අඟහරු",Mercury:"බුධ",Jupiter:"ගුරු",Venus:"ශුක්‍ර",Saturn:"සෙනසුරු",Rahu:"රාහු",Ketu:"කේතු",Uranus:"යුරේනස්",Neptune:"නෙප්චූන්",Pluto:"ප්ලූටෝ",Ascendant:"ලග්න",SUN:"සූර්ය",MOON:"චන්ද්‍ර",MARS:"අඟහරු",MERCURY:"බුධ",JUPITER:"ගුරු",VENUS:"ශුක්‍ර",SATURN:"සෙනසුරු",URANUS:"යුරේනස්",NEPTUNE:"නෙප්චූන්",PLUTO:"ප්ලූටෝ",Surya:"සූර්ය",Chandra:"චන්ද්‍ර",Mangal:"අඟහරු",Budha:"බුධ",Guru:"ගුරු",Shukra:"ශුක්‍ර",Shani:"සෙනසුරු",uranus:"යුරේනස්",neptune:"නෙප්චූන්",pluto:"ප්ලූටෝ",chiron:"චිරෝන්",sirius:"සිරියස්",Chiron:"චිරෝන්",Sirius:"සිරියස්",CHIRON:"චිරෝන්",SIRIUS:"සිරියස්"};if("si"===c&&a[e])return console.log("\uD83E\uDE90 Direct Sinhala planet translation:",{originalPlanet:e,translatedPlanet:a[e],language:c}),a[e];let t={Sun:d("sun"),Moon:d("moon"),Mars:d("mars"),Mercury:d("mercury"),Jupiter:d("jupiter"),Venus:d("venus"),Saturn:d("saturn"),Rahu:d("rahu"),Ketu:d("ketu"),Uranus:d("uranus"),Neptune:d("neptune"),Pluto:d("pluto"),Ascendant:d("ascendant"),SUN:d("sun"),MOON:d("moon"),MARS:d("mars"),MERCURY:d("mercury"),JUPITER:d("jupiter"),VENUS:d("venus"),SATURN:d("saturn"),Surya:d("sun"),Chandra:d("moon"),Mangal:d("mars"),Budha:d("mercury"),Guru:d("jupiter"),Shukra:d("venus"),Shani:d("saturn"),chiron:d("chiron"),sirius:d("sirius"),Chiron:d("chiron"),Sirius:d("sirius"),CHIRON:d("chiron"),SIRIUS:d("sirius"),uranus:d("uranus"),neptune:d("neptune"),pluto:d("pluto"),Uranus:d("uranus"),Neptune:d("neptune"),Pluto:d("pluto"),URANUS:d("uranus"),NEPTUNE:d("neptune"),PLUTO:d("pluto")};if(console.log("\uD83E\uDE90 Planet Translation:",{originalPlanet:e,translatedPlanet:t[e]||e,language:c,foundMapping:!!t[e],availableKeys:Object.keys(t).slice(0,10)}),t[e])return t[e];let s=e.trim();if(t[s])return t[s];let r=e.toLowerCase();if(t[r])return t[r];let l=e.charAt(0).toUpperCase()+e.slice(1).toLowerCase();return t[l]?t[l]:(console.warn("\uD83D\uDEA8 No translation found for planet:",e),e)},u=e=>({Ar:d("ar"),Ta:d("ta"),Ge:d("ge"),Ca:d("ca"),Le:d("le"),Vi:d("vi"),Li:d("li"),Sc:d("sc"),Sa:d("sa"),Cp:d("cp"),Aq:d("aq"),Pi:d("pi"),Total:d("total")})[e]||e,x=e=>({Own:d("own"),Friendly:d("friendly"),Enemy:d("enemy"),Neutral:d("neutral")})[e]||e,m=e=>{if(!e)return e;let a={Atma:"ආත්මා",Alma:"ආත්මා",Dara:"දාර",Gnati:"ඥාති",Matru:"මාතෘ",Putra:"පුත්‍ර",Amatya:"අමාත්‍ය",Bhratru:"භ්‍රාතෘ"};return"si"===c&&a[e]?a[e]:({Alma:d("alma"),Dara:d("dara"),Gnati:d("gnati"),Matru:d("matru"),Putra:d("putra"),Amatya:d("amatya"),Bhratru:d("bhratru")})[e]||e},g=e=>{let a={Swapna:d("swapna"),Jaagrat:d("jaagrat"),Yuva:d("yuva"),Deena:d("deena"),Kumar:d("kumar"),Bala:d("bala"),Swatha:d("swatha"),Mrat:d("mrat"),Muditha:d("muditha"),Susupla:d("susupla"),Vadha:d("vadha"),Deepta:d("deepta"),Khal:d("khal"),Shant:d("shant"),Susupta:d("susupla"),Vradha:d("vadha")};if(console.log("\uD83D\uDD2E Avastha Translation:",{originalAvastha:e,translatedAvastha:a[e]||e,language:c,foundMapping:!!a[e]}),a[e])return a[e];let t=e.trim();if(a[t])return a[t];for(let t of[e.toLowerCase(),e.toUpperCase(),e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()])if(a[t])return console.log("\uD83D\uDD2E Found avastha translation with variation:",{original:e,variation:t,translated:a[t]}),a[t];return console.warn("\uD83D\uDEA8 No translation found for avastha:",e),e},p=e=>{if(!e)return e;let a={Aries:"මේෂ",Taurus:"වෘෂභ",Gemini:"මිථුන",Cancer:"කටක",Leo:"සිංහ",Virgo:"කන්‍යා",Libra:"තුලා",Scorpio:"වෘශ්චික",Sagittarius:"ධනු",Capricorn:"මකර",Aquarius:"කුම්භ",Pisces:"මීන"};if("si"===c&&a[e])return console.log("\uD83D\uDD24 Direct Sinhala translation:",{originalSign:e,translatedSign:a[e],language:c}),a[e];let t={Aries:d("aries"),Taurus:d("taurus"),Gemini:d("gemini"),Cancer:d("cancer"),Leo:d("leo"),Virgo:d("virgo"),Libra:d("libra"),Scorpio:d("scorpio"),Sagittarius:d("sagittarius"),Capricorn:d("capricorn"),Aquarius:d("aquarius"),Pisces:d("pisces"),aries:d("aries"),taurus:d("taurus"),gemini:d("gemini"),cancer:d("cancer"),leo:d("leo"),virgo:d("virgo"),libra:d("libra"),scorpio:d("scorpio"),sagittarius:d("sagittarius"),capricorn:d("capricorn"),aquarius:d("aquarius"),pisces:d("pisces"),ARIES:d("aries"),TAURUS:d("taurus"),GEMINI:d("gemini"),CANCER:d("cancer"),LEO:d("leo"),VIRGO:d("virgo"),LIBRA:d("libra"),SCORPIO:d("scorpio"),SAGITTARIUS:d("sagittarius"),CAPRICORN:d("capricorn"),AQUARIUS:d("aquarius"),PISCES:d("pisces")};if(console.log("\uD83D\uDD24 AstrologicalTables Zodiac Sign Translation:",{originalSign:e,translatedSign:t[e]||e,availableTranslations:Object.keys(t),language:c}),t[e])return t[e];let s=e.trim();if(t[s])return t[s];for(let a of[e.toLowerCase(),e.toUpperCase(),e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()])if(t[a])return console.log("\uD83D\uDD24 Found sign translation with variation:",{original:e,variation:a,translated:t[a]}),t[a];return console.warn("\uD83D\uDEA8 No translation found for zodiac sign:",e),e},b=e=>{if(!e)return e;let a={Ashwini:"අශ්විනී",Bharani:"භරණී",Krittika:"කෘත්තිකා",Rohini:"රෝහිණී",Mrigashira:"මෘගශිරා",Ardra:"ආර්ද්‍රා",Punarvasu:"පුනර්වසු",Pushya:"පුෂ්‍යා",Ashlesha:"ආශ්ලේෂා",Magha:"මඝා","Purva Phalguni":"පූර්ව ඵල්ගුනී","Uttara Phalguni":"උත්තර ඵල්ගුනී",Hasta:"හස්ත",Chitra:"චිත්‍රා",Swati:"ස්වාතී",Vishakha:"විශාඛා",Anuradha:"අනුරාධා",Jyeshtha:"ජ්‍යේෂ්ඨා",Mula:"මූලා","Purva Ashadha":"පූර්ව ආෂාඪා","Uttara Ashadha":"උත්තර ආෂාඪා",Shravana:"ශ්‍රවණ",Dhanishta:"ධනිෂ්ඨා",Shatabhisha:"ශතභිෂා","Purva Bhadrapada":"පූර්ව භද්‍රපදා","Uttara Bhadrapada":"උත්තර භද්‍රපදා",Revati:"රේවතී"};if("si"===c&&a[e])return console.log("\uD83C\uDF1F Direct Sinhala nakshatra translation:",{originalNakshatra:e,translatedNakshatra:a[e],language:c}),a[e];let t={Ashwini:d("ashwini"),Bharani:d("bharani"),Krittika:d("krittika"),Rohini:d("rohini"),Mrigashira:d("mrigashira"),Ardra:d("ardra"),Punarvasu:d("punarvasu"),Pushya:d("pushya"),Ashlesha:d("ashlesha"),Magha:d("magha"),"Purva Phalguni":d("purva_phalguni"),"Uttara Phalguni":d("uttara_phalguni"),Hasta:d("hasta"),Chitra:d("chitra"),Swati:d("swati"),Vishakha:d("vishakha"),Anuradha:d("anuradha"),Jyeshtha:d("jyeshtha"),Mula:d("mula"),"Purva Ashadha":d("purva_ashadha"),"Uttara Ashadha":d("uttara_ashadha"),Shravana:d("shravana"),Dhanishta:d("dhanishta"),Shatabhisha:d("shatabhisha"),"Purva Bhadrapada":d("purva_bhadrapada"),"Uttara Bhadrapada":d("uttara_bhadrapada"),Revati:d("revati"),Ashvini:d("ashwini"),Kritika:d("krittika"),Mrigasira:d("mrigashira"),Arudra:d("ardra"),Punarvasu:d("punarvasu"),Pushyami:d("pushya"),Ayilyam:d("ashlesha"),Makam:d("magha"),Pooram:d("purva_phalguni"),Uthram:d("uttara_phalguni"),Hastham:d("hasta"),Chithira:d("chitra"),Chothi:d("swati"),Visakam:d("vishakha"),Anusham:d("anuradha"),Kettai:d("jyeshtha"),Moola:d("mula"),Pooradam:d("purva_ashadha"),Uthradam:d("uttara_ashadha"),Thiruvonam:d("shravana"),Avittam:d("dhanishta"),Chathayam:d("shatabhisha"),Poorattathi:d("purva_bhadrapada"),Uthrattathi:d("uttara_bhadrapada"),Revathi:d("revati")};if(console.log("\uD83C\uDF1F AstrologicalTables Nakshatra Translation:",{originalNakshatra:e,translatedNakshatra:t[e]||e,availableTranslations:Object.keys(t),language:c}),t[e])return t[e];let s=e.trim();if(t[s])return t[s];for(let a of[e.toLowerCase(),e.toUpperCase(),e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()])if(t[a])return console.log("\uD83C\uDF1F Found nakshatra translation with variation:",{original:e,variation:a,translated:t[a]}),t[a];return console.warn("\uD83D\uDEA8 No translation found for nakshatra:",e),e},f=e=>{switch(e){case"Own":return"text-green-400";case"Friendly":return"text-blue-400";case"Enemy":return"text-red-400";case"Neutral":return"text-yellow-400";default:return"text-gray-400"}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t?(0,s.jsxs)("div",{className:"bg-gradient-to-br from-yellow-900/30 to-orange-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-yellow-500/20 shadow-2xl",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center",children:d("karaka")}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm md:text-base",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b-2 border-orange-500/40",children:[(0,s.jsx)("th",{className:"text-left py-3 text-orange-200 font-bold",children:d("karak")}),(0,s.jsx)("th",{className:"text-left py-3 text-orange-200 font-bold",children:d("sthir")}),(0,s.jsx)("th",{className:"text-left py-3 text-orange-200 font-bold",children:d("chara")})]})}),(0,s.jsx)("tbody",{children:Object.entries(t).map(e=>{let[a,t]=e;return(0,s.jsxs)("tr",{className:"border-b border-orange-500/20 hover:bg-orange-500/10 transition-colors",children:[(0,s.jsx)("td",{className:"py-3 text-white font-medium",children:m(a)}),(0,s.jsx)("td",{className:"py-3 text-yellow-100 font-medium",children:h(t.sthir)}),(0,s.jsx)("td",{className:"py-3 text-yellow-100 font-medium",children:h(t.chara)})]},a)})})]})})]}):null,r?(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-blue-500/20 shadow-2xl",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent text-center",children:d("avastha")}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm md:text-base",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b-2 border-blue-500/40",children:[(0,s.jsx)("th",{className:"text-left py-3 text-blue-200 font-bold",children:d("planets")}),(0,s.jsx)("th",{className:"text-left py-3 text-blue-200 font-bold",children:d("jagrat")}),(0,s.jsx)("th",{className:"text-left py-3 text-blue-200 font-bold",children:d("baladi")}),(0,s.jsx)("th",{className:"text-left py-3 text-blue-200 font-bold",children:d("deeptadi")})]})}),(0,s.jsx)("tbody",{children:Object.entries(r).map(e=>{let[a,t]=e;return(0,s.jsxs)("tr",{className:"border-b border-blue-500/20 hover:bg-blue-500/10 transition-colors",children:[(0,s.jsx)("td",{className:"py-3 text-white font-medium",children:h(a)}),(0,s.jsx)("td",{className:"py-3 text-blue-100 font-medium",children:g(t.jagrat)}),(0,s.jsx)("td",{className:"py-3 text-blue-100 font-medium",children:g(t.baladi)}),(0,s.jsx)("td",{className:"py-3 text-blue-100 font-medium",children:g(t.deeptadi)})]},a)})})]})})]}):null]}),l?(console.log("\uD83E\uDDEA Testing translation function:"),console.log("  libra ->",d("libra")),console.log("  pisces ->",d("pisces")),console.log('  translateSign("Libra") ->',p("Libra")),console.log('  translateSign("Pisces") ->',p("Pisces")),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-900/30 to-teal-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-green-500/20 shadow-2xl",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-green-300 to-teal-300 bg-clip-text text-transparent text-center",children:d("planetary_details")}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-sm md:text-base",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b-2 border-green-500/40",children:[(0,s.jsx)("th",{className:"text-left py-3 text-green-200 font-bold",children:d("planets")}),(0,s.jsx)("th",{className:"text-center py-3 text-green-200 font-bold",children:d("c_r")}),(0,s.jsx)("th",{className:"text-left py-3 text-green-200 font-bold",children:d("rashi")}),(0,s.jsx)("th",{className:"text-left py-3 text-green-200 font-bold",children:d("longitude")}),(0,s.jsx)("th",{className:"text-left py-3 text-green-200 font-bold",children:d("nakshatra")}),(0,s.jsx)("th",{className:"text-center py-3 text-green-200 font-bold",children:d("pada")}),(0,s.jsx)("th",{className:"text-left py-3 text-green-200 font-bold",children:d("relation")})]})}),(0,s.jsx)("tbody",{children:l.map(e=>(0,s.jsxs)("tr",{className:"border-b border-green-500/20 hover:bg-green-500/10 transition-colors",children:[(0,s.jsx)("td",{className:"py-3 text-white font-medium",children:h(e.planet)}),(0,s.jsxs)("td",{className:"py-3 text-center",children:[(0,s.jsx)("span",{className:e.combust?"text-red-300 font-bold":"text-gray-400",children:e.combust?d("combust").charAt(0).toUpperCase():"-"}),(0,s.jsx)("span",{className:"ml-2",children:(0,s.jsx)("span",{className:e.retrograde?"text-red-300 font-bold":"text-gray-400",children:e.retrograde?d("retrograde_short"):"-"})})]}),(0,s.jsx)("td",{className:"py-3 text-green-100 font-medium",children:(()=>{let a=p(e.rashi);return console.log("\uD83D\uDD0D Rashi translation:",{original:e.rashi,translated:a,language:c,directTranslation:d("libra")}),a})()}),(0,s.jsx)("td",{className:"py-3 text-green-100 font-medium",children:e.longitude}),(0,s.jsx)("td",{className:"py-3 text-green-100 font-medium",children:b(e.nakshatra)}),(0,s.jsx)("td",{className:"py-3 text-center text-green-100 font-medium",children:e.pada}),(0,s.jsx)("td",{className:"py-3",children:(0,s.jsx)("span",{className:f(e.relation),children:x(e.relation)})})]},e.planet))})]})})]})):null,n?(0,s.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-purple-500/20 shadow-2xl",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent text-center",children:d("vimshottari_dasha")}),(0,s.jsxs)("div",{className:"mb-6 p-6 bg-purple-800/30 backdrop-blur-sm rounded-xl border border-purple-500/20",children:[(0,s.jsxs)("p",{className:"text-purple-100 text-lg",children:[(0,s.jsxs)("span",{className:"font-bold text-purple-200",children:[d("balance_of_dasha"),":"]})," ",(a=n.balance)?a.replace(/years?/g,d("years")).replace(/months?/g,d("months")).replace(/days?/g,d("days")):a]}),(0,s.jsxs)("p",{className:"text-purple-100 text-lg mt-2",children:[(0,s.jsxs)("span",{className:"font-bold text-purple-200",children:[d("current_dasha"),":"]})," ",h(n.currentDasha)]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-3",children:n.periods.slice(0,9).map((e,a)=>(0,s.jsxs)("div",{className:"flex justify-between items-center p-4 bg-purple-800/20 backdrop-blur-sm rounded-lg border border-purple-500/10 hover:bg-purple-500/20 transition-colors",children:[(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:h(e.planet)}),(0,s.jsxs)("span",{className:"text-purple-100 font-medium",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]},a))})]}):null,o?(0,s.jsxs)("div",{className:"bg-gradient-to-br from-indigo-900/20 to-blue-900/20 rounded-xl p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:d("prastharashtakvarga")}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-yellow-300 mb-2",children:d("sun").toUpperCase()}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs border border-yellow-500/30",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-yellow-900/20",children:[(0,s.jsx)("th",{className:"border border-yellow-500/30 p-1 text-yellow-300"}),["Ar","Ta","Ge","Ca","Le","Vi","Li","Sc","Sa","Cp","Aq","Pi","Total"].map(e=>(0,s.jsx)("th",{className:"border border-yellow-500/30 p-1 text-yellow-300",children:u(e)},e))]})}),(0,s.jsxs)("tbody",{children:[["SUN","MOON","MARS","MERCURY","JUPITER","VENUS","SATURN","Ascendant"].map((e,a)=>{var t,r;return(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-white font-medium",children:h(e)}),(null==(t=o.sunTable[a])?void 0:t.map((e,a)=>(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-center text-yellow-200",children:e},a)))||Array(12).fill(0).map((e,a)=>(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-center text-yellow-200",children:"0"},a)),(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold",children:(null==(r=o.sunTable[a])?void 0:r.reduce((e,a)=>e+a,0))||0})]},e)}),(0,s.jsxs)("tr",{className:"bg-yellow-900/20",children:[(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-yellow-300 font-bold",children:d("total")}),Array(12).fill(0).map((e,a)=>{let t=o.sunTable.reduce((e,t)=>e+(t[a]||0),0);return(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold",children:t},a)}),(0,s.jsx)("td",{className:"border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold",children:o.sarvashtakavarga.reduce((e,a)=>e+a,0)})]})]})]})})]}),(0,s.jsxs)("div",{className:"mt-4 p-4 bg-indigo-800/20 rounded-lg",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-indigo-300 mb-2",children:d("sarvashtakavarga_summary")}),(0,s.jsx)("div",{className:"grid grid-cols-6 gap-2 text-sm",children:o.sarvashtakavarga.map((e,a)=>(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"text-indigo-300",children:[d("house")," ",a+1]}),(0,s.jsx)("div",{className:"text-white font-bold",children:e})]},a))})]})]}):null]})}function B(e){let{birthChart:a,className:t="",loading:l=!1,error:n=null}=e,[o,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)("charts"),{t:x}=(0,O.LL)(),{language:m}=(0,i.o)(),g=e=>{if(!e)return e;let a={Aries:"මේෂ",Taurus:"වෘෂභ",Gemini:"මිථුන",Cancer:"කටක",Leo:"සිංහ",Virgo:"කන්‍යා",Libra:"තුලා",Scorpio:"වෘශ්චික",Sagittarius:"ධනු",Capricorn:"මකර",Aquarius:"කුම්භ",Pisces:"මීන"};return"si"===m&&a[e]?a[e]:({Aries:x("aries"),Taurus:x("taurus"),Gemini:x("gemini"),Cancer:x("cancer"),Leo:x("leo"),Virgo:x("virgo"),Libra:x("libra"),Scorpio:x("scorpio"),Sagittarius:x("sagittarius"),Capricorn:x("capricorn"),Aquarius:x("aquarius"),Pisces:x("pisces")})[e]||e},p=e=>{if(!e)return e;let a={Sun:"සූර්ය",Moon:"චන්ද්‍ර",Mars:"අඟහරු",Mercury:"බුධ",Jupiter:"ගුරු",Venus:"ශුක්‍ර",Saturn:"සෙනසුරු",Rahu:"රාහු",Ketu:"කේතු",Uranus:"යුරේනස්",Neptune:"නෙප්චූන්",Pluto:"ප්ලූටෝ",Surya:"සූර්ය",Chandra:"චන්ද්‍ර",Mangal:"අඟහරු",Budha:"බුධ",Guru:"ගුරු",Shukra:"ශුක්‍ර",Shani:"සෙනසුරු",chiron:"චිරෝන්",sirius:"සිරියස්",Chiron:"චිරෝන්",Sirius:"සිරියස්",uranus:"යුරේනස්",neptune:"නෙප්චූන්",pluto:"ප්ලූටෝ"};return"si"===m&&a[e]?a[e]:({Sun:x("sun"),Moon:x("moon"),Mars:x("mars"),Mercury:x("mercury"),Jupiter:x("jupiter"),Venus:x("venus"),Saturn:x("saturn"),Rahu:x("rahu"),Ketu:x("ketu"),Uranus:x("uranus"),Neptune:x("neptune"),Pluto:x("pluto"),Surya:x("sun"),Chandra:x("moon"),Mangal:x("mars"),Budha:x("mercury"),Guru:x("jupiter"),Shukra:x("venus"),Shani:x("saturn"),chiron:x("chiron"),sirius:x("sirius"),Chiron:x("chiron"),Sirius:x("sirius"),uranus:x("uranus"),neptune:x("neptune"),pluto:x("pluto")})[e]||e},b=e=>{if(!e)return e;let a={Ashwini:"අශ්විනී",Bharani:"භරණී",Krittika:"කෘත්තිකා",Rohini:"රෝහිණී",Mrigashira:"මෘගශිරා",Ardra:"ආර්ද්‍රා",Punarvasu:"පුනර්වසු",Pushya:"පුෂ්‍යා",Ashlesha:"ආශ්ලේෂා",Magha:"මඝා","Purva Phalguni":"පූර්ව ඵල්ගුනී","Uttara Phalguni":"උත්තර ඵල්ගුනී",Hasta:"හස්ත",Chitra:"චිත්‍රා",Swati:"ස්වාතී",Vishakha:"විශාඛා",Anuradha:"අනුරාධා",Jyeshtha:"ජ්‍යේෂ්ඨා",Mula:"මූලා","Purva Ashadha":"පූර්ව ආෂාඪා","Uttara Ashadha":"උත්තර ආෂාඪා",Shravana:"ශ්‍රවණ",Dhanishta:"ධනිෂ්ඨා",Shatabhisha:"ශතභිෂා","Purva Bhadrapada":"පූර්ව භද්‍රපදා","Uttara Bhadrapada":"උත්තර භද්‍රපදා",Revati:"රේවතී"};return"si"===m&&a[e]?a[e]:({Ashwini:x("ashwini"),Bharani:x("bharani"),Krittika:x("krittika"),Rohini:x("rohini"),Mrigashira:x("mrigashira"),Ardra:x("ardra"),Punarvasu:x("punarvasu"),Pushya:x("pushya"),Ashlesha:x("ashlesha"),Magha:x("magha"),"Purva Phalguni":x("purva_phalguni"),"Uttara Phalguni":x("uttara_phalguni"),Hasta:x("hasta"),Chitra:x("chitra"),Swati:x("swati"),Vishakha:x("vishakha"),Anuradha:x("anuradha"),Jyeshtha:x("jyeshtha"),Mula:x("mula"),"Purva Ashadha":x("purva_ashadha"),"Uttara Ashadha":x("uttara_ashadha"),Shravana:x("shravana"),Dhanishta:x("dhanishta"),Shatabhisha:x("shatabhisha"),"Purva Bhadrapada":x("purva_bhadrapada"),"Uttara Bhadrapada":x("uttara_bhadrapada"),Revati:x("revati")})[e]||e};if(l)return(0,s.jsx)("div",{className:"bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ".concat(t),children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)(h.A,{className:"mx-auto mb-4 text-purple-400 animate-spin",size:48}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:x("loading_birth_chart")}),(0,s.jsx)("p",{className:"text-purple-200",children:x("calculating_cosmic_blueprint")})]})});if(n)return(0,s.jsx)("div",{className:"bg-gradient-to-br from-red-900/40 to-orange-900/40 backdrop-blur-sm rounded-2xl border border-red-500/20 shadow-2xl ".concat(t),children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)(S.A,{className:"mx-auto mb-4 text-red-400",size:48}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:x("error_loading_birth_chart")}),(0,s.jsx)("p",{className:"text-red-200 mb-4",children:n}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors",children:x("try_again")})]})});let f={ascendant:a.ascendant,moonSign:a.moonSign,sunSign:a.sunSign,planets:a.planetPositions||[],houses:a.housePositions||[],aspects:a.aspects||[],nakshatras:a.nakshatras||[],dashas:a.dashas||[],lagnaChart:a.lagnaChart?L(a.lagnaChart,x,m):void 0,navamsaChart:a.navamsaChart?L(a.navamsaChart,x,m):void 0,chandraChart:a.chandraChart?L(a.chandraChart,x,m):void 0,karakTable:a.karakTable,avasthaTable:a.avasthaTable,planetaryDetails:a.planetaryDetails,vimshottariDasha:a.vimshottariDasha,ashtakavarga:a.ashtakavarga},y=f.lagnaChart||f.navamsaChart||f.chandraChart,v=a.planetPositions&&a.planetPositions.length>0;return console.log("\uD83D\uDD0D BirthChart component - Enhanced data check:",{hasEnhancedData:y,hasBasicData:v,lagnaChart:!!f.lagnaChart,navamsaChart:!!f.navamsaChart,chandraChart:!!f.chandraChart,planetaryDetails:!!f.planetaryDetails,karakTable:!!f.karakTable,avasthaTable:!!f.avasthaTable,vimshottariDasha:!!f.vimshottariDasha,ashtakavarga:!!f.ashtakavarga}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-blue-900/40 backdrop-blur-sm rounded-2xl border border-purple-500/20 shadow-2xl ".concat(t),children:[(0,s.jsxs)("div",{className:"p-6 border-b border-purple-500/20",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,s.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center",children:["⭐ ",x("your_birth_chart_awaits")]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20",children:[(0,s.jsxs)("div",{className:"text-xs md:text-sm text-purple-300 mb-1",children:[x("ascendant")," (",x("rising_sign"),")"]}),(0,s.jsx)("div",{className:"text-base md:text-lg font-semibold text-white",children:g(a.ascendant)})]}),(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20",children:[(0,s.jsx)("div",{className:"text-xs md:text-sm text-purple-300 mb-1",children:x("moon_sign")}),(0,s.jsx)("div",{className:"text-base md:text-lg font-semibold text-white",children:g(a.moonSign)})]}),(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-3 md:p-4 border border-purple-500/20 sm:col-span-2 lg:col-span-1",children:[(0,s.jsx)("div",{className:"text-xs md:text-sm text-purple-300 mb-1",children:x("sun_sign")}),(0,s.jsx)("div",{className:"text-base md:text-lg font-semibold text-white",children:g(a.sunSign)})]})]}),a.user&&(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-300",children:[a.user.birthDate&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(A.A,{size:16,className:"mr-1"}),new Date(a.user.birthDate).toLocaleDateString()]}),a.user.birthTime&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(C.A,{size:16,className:"mr-1"}),a.user.birthTime]}),a.user.birthPlace&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(_.A,{size:16,className:"mr-1"}),a.user.birthPlace]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-4",children:[(0,s.jsx)("button",{onClick:()=>u("charts"),className:"px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ".concat("charts"===c?"bg-purple-600 text-white":"bg-purple-800/20 text-purple-200 hover:bg-purple-700/30"),children:x("charts")}),(0,s.jsx)("button",{onClick:()=>u("tables"),className:"px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ".concat("tables"===c?"bg-purple-600 text-white":"bg-purple-800/20 text-purple-200 hover:bg-purple-700/30"),children:x("tables")}),(0,s.jsx)("button",{onClick:()=>u("readings"),className:"px-3 md:px-4 py-2 rounded-lg text-xs md:text-sm font-medium transition-all ".concat("readings"===c?"bg-purple-600 text-white":"bg-purple-800/20 text-purple-200 hover:bg-purple-700/30"),children:x("readings")})]})]}),(0,s.jsxs)("div",{className:"p-4 md:p-6",children:["charts"===c&&(()=>{try{if(!y)return(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-6 border border-purple-500/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("enhanced_vedic_charts")}),(0,s.jsx)("p",{className:"text-purple-200 mb-4",children:x("enhanced_vedic_charts_not_available")}),(0,s.jsx)("p",{className:"text-sm text-purple-300 mb-4",children:x("basic_system_generated")}),(0,s.jsxs)("div",{className:"text-xs text-purple-400 bg-purple-900/30 rounded p-3",children:[(0,s.jsxs)("p",{className:"mb-1",children:[x("debug_info"),":"]}),(0,s.jsxs)("p",{children:[x("lagna_chart_available"),": ",f.lagnaChart?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("navamsa_chart_available"),": ",f.navamsaChart?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("chandra_chart_available"),": ",f.chandraChart?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("planet_positions_available"),": ",v?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]})]})]})});return console.log("\uD83C\uDFA8 Rendering enhanced charts"),console.log("\uD83D\uDD0D Enhanced chart data:",{lagnaChart:!!f.lagnaChart,navamsaChart:!!f.navamsaChart,chandraChart:!!f.chandraChart}),(0,s.jsxs)("div",{className:"w-full space-y-8 md:space-y-12",style:{display:"block",visibility:"visible"},children:[(0,s.jsxs)("div",{className:"text-center mb-6 md:mb-8",children:[(0,s.jsxs)("h2",{className:"text-2xl md:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-300 via-blue-300 to-purple-300 bg-clip-text text-transparent mb-4",children:["⭐ ",x("birth_chart")," (",x("handahana"),") ⭐"]}),(0,s.jsx)("p",{className:"text-gray-300 text-base md:text-lg max-w-3xl mx-auto",children:(0,s.jsx)(j,{text:x("personalized_vedic_charts_description")})})]}),(0,s.jsxs)("div",{className:"w-full space-y-8 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-12",style:{display:"block !important",visibility:"visible !important"},children:[f.lagnaChart&&(0,s.jsx)("div",{className:"w-full",style:{display:"block !important",visibility:"visible !important"},children:(0,s.jsx)(G,{chartData:f.lagnaChart,title:"✨ ".concat(x("lagna_chart")," (D1) ✨"),className:""})}),f.navamsaChart&&(0,s.jsx)("div",{className:"w-full",style:{display:"block !important",visibility:"visible !important"},children:(0,s.jsx)(G,{chartData:f.navamsaChart,title:"✨ ".concat(x("navamsa_chart")," (D9) ✨"),className:""})})]}),f.chandraChart&&(0,s.jsx)("div",{className:"w-full mt-8 lg:mt-12",style:{display:"block !important",visibility:"visible !important"},children:(0,s.jsx)(G,{chartData:f.chandraChart,title:"✨ ".concat(x("chandra_chart")," ✨"),className:""})})]})}catch(e){return console.error("Error rendering charts:",e),(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-red-800/20 rounded-lg p-6 border border-red-500/20",children:[(0,s.jsx)(S.A,{className:"mx-auto mb-4 text-red-400",size:48}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("error_loading_charts")}),(0,s.jsx)("p",{className:"text-red-200",children:x("error_loading_charts_message")})]})})}})(),"tables"===c&&(()=>{try{if(!y)return(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-6 border border-purple-500/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("astrological_data_tables")}),(0,s.jsx)("p",{className:"text-purple-200 mb-4",children:x("astrological_tables_not_available")}),(0,s.jsx)("p",{className:"text-sm text-purple-300 mb-4",children:x("enhanced_tables_missing")}),(0,s.jsxs)("div",{className:"text-xs text-purple-400 bg-purple-900/30 rounded p-3",children:[(0,s.jsxs)("p",{className:"mb-1",children:[x("debug_info"),":"]}),(0,s.jsxs)("p",{children:[x("karaka_table_available"),": ",f.karakTable?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("avastha_table_available"),": ",f.avasthaTable?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("planetary_details_available"),": ",f.planetaryDetails?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("vimshottari_dasha_available"),": ",f.vimshottariDasha?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]}),(0,s.jsxs)("p",{children:[x("ashtakavarga_available"),": ",f.ashtakavarga?"✅ ".concat(x("available")):"❌ ".concat(x("missing"))]})]})]})});return(0,s.jsx)(z,{karakTable:f.karakTable,avasthaTable:f.avasthaTable,planetaryDetails:f.planetaryDetails,vimshottariDasha:f.vimshottariDasha,ashtakavarga:f.ashtakavarga})}catch(e){return console.error("Error rendering tables:",e),(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-red-800/20 rounded-lg p-6 border border-red-500/20",children:[(0,s.jsx)(S.A,{className:"mx-auto mb-4 text-red-400",size:48}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("error_loading_tables")}),(0,s.jsx)("p",{className:"text-red-200",children:x("error_loading_tables_message")})]})})}})(),"readings"===c&&(()=>{try{let{readingsEn:e,readingsSi:t,generalReading:r,strengthsWeaknesses:l,careerGuidance:i,relationshipGuidance:n,healthGuidance:o}=a,d=e||t;if(!(r||l||i||n||o)&&!d)return(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-6 border border-purple-500/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("birth_chart_readings")}),(0,s.jsx)("p",{className:"text-purple-200 mb-4",children:x("readings_not_available")}),(0,s.jsx)("p",{className:"text-sm text-purple-300",children:x("readings_generated_automatically")})]})});return(0,s.jsxs)("div",{className:"space-y-6",children:[(r||d&&((null==e?void 0:e.generalReading)||(null==t?void 0:t.generalReading)))&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-xl p-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCD6"}),x("general_reading")]}),(0,s.jsx)("div",{className:"text-gray-300 leading-relaxed",children:(0,s.jsx)(j,{text:r||(null==e?void 0:e.generalReading)||"",translations:{en:(null==e?void 0:e.generalReading)||r,si:null==t?void 0:t.generalReading},className:"whitespace-pre-line"})})]}),(l||d&&((null==e?void 0:e.strengthsWeaknesses)||(null==t?void 0:t.strengthsWeaknesses)))&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-900/20 to-teal-900/20 rounded-xl p-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCAA"}),x("strengths_weaknesses")]}),(0,s.jsx)("div",{className:"text-gray-300 leading-relaxed",children:(0,s.jsx)(j,{text:l||(null==e?void 0:e.strengthsWeaknesses)||"",translations:{en:(null==e?void 0:e.strengthsWeaknesses)||l,si:null==t?void 0:t.strengthsWeaknesses},className:"whitespace-pre-line"})})]}),(i||d&&((null==e?void 0:e.careerGuidance)||(null==t?void 0:t.careerGuidance)))&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-orange-900/20 to-red-900/20 rounded-xl p-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCBC"}),x("career_guidance")]}),(0,s.jsx)("div",{className:"text-gray-300 leading-relaxed",children:(0,s.jsx)(j,{text:i||(null==e?void 0:e.careerGuidance)||"",translations:{en:(null==e?void 0:e.careerGuidance)||i,si:null==t?void 0:t.careerGuidance},className:"whitespace-pre-line"})})]}),(n||d&&((null==e?void 0:e.relationshipGuidance)||(null==t?void 0:t.relationshipGuidance)))&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-pink-900/20 to-rose-900/20 rounded-xl p-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDC95"}),x("relationship_guidance")]}),(0,s.jsx)("div",{className:"text-gray-300 leading-relaxed",children:(0,s.jsx)(j,{text:n||(null==e?void 0:e.relationshipGuidance)||"",translations:{en:(null==e?void 0:e.relationshipGuidance)||n,si:null==t?void 0:t.relationshipGuidance},className:"whitespace-pre-line"})})]}),(o||d&&((null==e?void 0:e.healthGuidance)||(null==t?void 0:t.healthGuidance)))&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-emerald-900/20 to-green-900/20 rounded-xl p-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"\uD83C\uDF3F"}),x("health_guidance")]}),(0,s.jsx)("div",{className:"text-gray-300 leading-relaxed",children:(0,s.jsx)(j,{text:o||(null==e?void 0:e.healthGuidance)||"",translations:{en:(null==e?void 0:e.healthGuidance)||o,si:null==t?void 0:t.healthGuidance},className:"whitespace-pre-line"})})]})]})}catch(e){return console.error("Error rendering readings:",e),(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"bg-red-800/20 rounded-lg p-6 border border-red-500/20",children:[(0,s.jsx)(S.A,{className:"mx-auto mb-4 text-red-400",size:48}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:x("error_loading_readings")}),(0,s.jsx)("p",{className:"text-red-200",children:x("error_loading_readings_message")})]})})}})()]}),(0,s.jsxs)("div",{className:"border-t border-purple-500/20",children:[(0,s.jsxs)("button",{onClick:()=>d(!o),className:"w-full px-6 py-4 flex items-center justify-between text-left hover:bg-purple-800/20 transition-colors",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:x("planetary_positions")}),o?(0,s.jsx)(R.A,{className:"text-purple-400",size:20}):(0,s.jsx)(T.A,{className:"text-purple-400",size:20})]}),o&&(0,s.jsx)("div",{className:"px-6 pb-6",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:f.planets.map((e,a)=>(0,s.jsxs)("div",{className:"bg-purple-800/20 rounded-lg p-3 border border-purple-500/20",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"font-medium text-white",children:p(e.name)}),e.retrograde&&(0,s.jsx)("span",{className:"text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded",children:x("retrograde")})]}),(0,s.jsxs)("div",{className:"text-sm text-purple-200",children:[(0,s.jsxs)("div",{children:[x("sign"),": ",g(e.sign)]}),(0,s.jsxs)("div",{children:[x("house"),": ",e.house]}),(0,s.jsxs)("div",{children:[x("nakshatra"),": ",b(e.nakshatra)]}),(0,s.jsxs)("div",{children:[x("longitude"),": ",e.longitude.toFixed(2),"\xb0"]})]})]},a))})})]}),(0,s.jsx)("div",{className:"px-6 py-4 border-t border-purple-500/20 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-purple-300",children:[x("calculated_using_vedic")," •"," ",new Date(a.calculatedAt).toLocaleDateString()]})})]})}var F=t(5040),V=t(38564),W=t(99424),H=t(51976),J=t(17576),q=t(79397),K=t(15448),Y=t(33127),Q=t(80299),Z=t(17580);function X(){let{user:e,isAuthenticated:a,loading:t,getSessionTimeRemaining:h,isSessionExpired:x,logout:f}=function(){let[e,a]=(0,r.useState)(null),[t,s]=(0,r.useState)(!0),l=e=>!e||!e.sessionExpiry||new Date>new Date(e.sessionExpiry);(0,r.useEffect)(()=>{let e=localStorage.getItem("astroconnect_user");if(e)try{let t=JSON.parse(e);l(t)?(console.log("Session expired, removing stored user data"),localStorage.removeItem("astroconnect_user"),a(null)):a(t)}catch(e){console.error("Error parsing stored user data:",e),localStorage.removeItem("astroconnect_user")}s(!1)},[]),(0,r.useEffect)(()=>{if(!e||!e.sessionExpiry)return;let a=setInterval(()=>{l(e)&&(console.log("Session expired, logging out user"),n())},6e4),t=setTimeout(()=>{console.log("Session expired, logging out user"),n()},new Date(e.sessionExpiry).getTime()-Date.now());return()=>{clearInterval(a),clearTimeout(t)}},[e]);let i=async e=>{try{let t=await fetch("/api/auth/qr",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})}),s=await t.json();if(!s.success||!s.data)return{success:!1,error:s.error||"Authentication failed"};{let e=s.data;if(l(e))return{success:!1,error:"Session expired during authentication"};return a(e),localStorage.setItem("astroconnect_user",JSON.stringify(e)),console.log("User logged in with session expiring at: ".concat(e.sessionExpiry)),{success:!0}}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error occurred"}}},n=()=>{console.log("User logged out"),a(null),localStorage.removeItem("astroconnect_user")},o=!!e&&!l(e);return{user:e,loading:t,isAuthenticated:o,login:i,logout:n,updateUser:e=>{a(e),localStorage.setItem("astroconnect_user",JSON.stringify(e))},getSessionTimeRemaining:()=>e&&e.sessionExpiry?Math.max(0,Math.floor((new Date(e.sessionExpiry).getTime()-Date.now())/6e4)):0,isSessionExpired:()=>l(e)}}(),{language:S,setLanguage:A}=(0,i.o)(),{t:_}=(0,O.LL)(),{confirmLogout:R}=(0,n.G_)(),[T,D]=(0,r.useState)(null),[P,L]=(0,r.useState)(!0),[I,E]=(0,r.useState)(null),[M,U]=(0,r.useState)("horoscope"),[G,z]=(0,r.useState)(0),X=(0,l.useRouter)();(0,r.useEffect)(()=>{if(!a)return;let e=()=>{let e=h();if(z(e),e<=0||x()){console.log("Session expired, redirecting to home"),X.push("/");return}};e();let t=setInterval(e,6e4);return()=>clearInterval(t)},[a,h,x,X]);let[$,ee]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(!t&&!a)return void X.push("/");e&&e.languagePreference&&!$&&(console.log("\uD83C\uDF10 Setting initial user language preference:",e.languagePreference),A(e.languagePreference),ee(!0))},[e,a,t,X,A,$]),(0,r.useEffect)(()=>{e&&!t&&a&&ea()},[e,t,a]),(0,r.useEffect)(()=>{e&&!t&&a&&T&&(console.log("\uD83C\uDF10 Language changed, refetching dashboard data for translations"),ea())},[S]);let ea=async()=>{if(!e)return void m.warn(u.API,"Dashboard fetch skipped - no user",{userId:null==e?void 0:e.id});try{L(!0),p("/api/dashboard","GET",{userId:e.id,language:S},e.id);let c=await fetch("/api/dashboard?userId=".concat(e.id,"&language=").concat(S)),h=await c.json();if(h.success){var a,t,s,r,l,i,n,o,d;b("/api/dashboard",!0,{hasData:!!h.data,hasDailyReading:!!(null==(a=h.data)?void 0:a.dailyReading),hasBirthChart:!!(null==(t=h.data)?void 0:t.birthChart),hasTranslations:!!(null==(s=h.data)?void 0:s.dailyReadingTranslations)},e.id),m.info(u.DAILY_GUIDE,"Dashboard data loaded successfully",{dailyReadingDate:null==(l=h.data)||null==(r=l.dailyReading)?void 0:r.date,translationsAvailable:{en:!!(null==(n=h.data)||null==(i=n.dailyReadingTranslations)?void 0:i.en),si:!!(null==(d=h.data)||null==(o=d.dailyReadingTranslations)?void 0:o.si)}},e.id),D(h.data)}else b("/api/dashboard",!1,{error:h.error},e.id),E(h.error||"Failed to load dashboard data")}catch(a){y(a,"Dashboard data fetch",e.id),E("Failed to load dashboard data")}finally{L(!1)}},et=async a=>{g("Language Change Requested",{from:S,to:a},null==e?void 0:e.id),A(a),m.info(u.TRANSLATION,"Language context updated",{newLanguage:a},null==e?void 0:e.id),e&&(p("/api/dashboard","POST",{userId:e.id,language:a},e.id),fetch("/api/dashboard",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id,language:a})}).then(a=>{a.ok?b("/api/dashboard",!0,{action:"language_preference_update"},e.id):b("/api/dashboard",!1,{action:"language_preference_update"},e.id)}).catch(a=>{y(a,"Language preference update",e.id)}))},es=a=>{var t,s,r,l,i,n;if(console.log("\uD83D\uDD0D getDailyReadingTranslations called for field:",a),m.debug(u.TRANSLATION,"getDailyReadingTranslations called",{field:a,hasDashboardData:!!T,hasTranslations:!!(null==T?void 0:T.dailyReadingTranslations),translationsStructure:(null==T?void 0:T.dailyReadingTranslations)?{hasEn:!!T.dailyReadingTranslations.en,hasSi:!!T.dailyReadingTranslations.si,enKeys:T.dailyReadingTranslations.en?Object.keys(T.dailyReadingTranslations.en):[],siKeys:T.dailyReadingTranslations.si?Object.keys(T.dailyReadingTranslations.si):[]}:null},null==e?void 0:e.id),!(null==T?void 0:T.dailyReadingTranslations)){console.log("❌ No dailyReadingTranslations available for field:",a),m.warn(u.TRANSLATION,"No dailyReadingTranslations available",{field:a},null==e?void 0:e.id);return}let o={};(null==(t=T.dailyReadingTranslations.en)?void 0:t[a])?(o.en=T.dailyReadingTranslations.en[a],console.log("✅ Found English translation for field:",a),m.debug(u.TRANSLATION,"Found English translation",{field:a,value:(null==(l=o.en)?void 0:l.substring(0,50))+"..."},null==e?void 0:e.id)):(null==(s=T.dailyReading)?void 0:s[a])&&(o.en=T.dailyReading[a],console.log("⚠️ Using fallback English from dailyReading for field:",a),m.debug(u.TRANSLATION,"Using fallback English from dailyReading",{field:a,value:(null==(i=o.en)?void 0:i.substring(0,50))+"..."},null==e?void 0:e.id)),(null==(r=T.dailyReadingTranslations.si)?void 0:r[a])?(o.si=T.dailyReadingTranslations.si[a],console.log("✅ Found Sinhala translation for field:",a),m.debug(u.TRANSLATION,"Found Sinhala translation",{field:a,value:(null==(n=o.si)?void 0:n.substring(0,50))+"..."},null==e?void 0:e.id)):(console.log("❌ No Sinhala translation found for field:",a),m.warn(u.TRANSLATION,"No Sinhala translation found",{field:a},null==e?void 0:e.id));let d=Object.keys(o).length>0?o:void 0;return console.log("\uD83D\uDCCA getDailyReadingTranslations result for field:",a,"result:",d),m.debug(u.TRANSLATION,"getDailyReadingTranslations result",{field:a,hasResult:!!d,resultKeys:d?Object.keys(d):[],resultStructure:d},null==e?void 0:e.id),d},er=async()=>{await R()&&(f(),X.push("/"))},el=async()=>{if(e)try{console.log("\uD83D\uDD2E Calculating birth chart for user:",e.id);let a=await fetch("/api/birth-chart",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id})}),t=await a.json();t.success?(console.log("✅ Birth chart calculated successfully"),ea()):(console.error("❌ Failed to calculate birth chart:",t.error),alert("Failed to calculate birth chart: ".concat(t.error)))}catch(e){console.error("❌ Error calculating birth chart:",e),alert("Error calculating birth chart. Please try again.")}};if(t||P)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,s.jsx)(d.A,{message:_("loading_cosmic_insights")})});if(I)return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,s.jsx)(c.A,{title:_("error_loading_dashboard"),message:I,onRetry:ea})});if(!T||!e)return null;let ei=o.Wh[e.zodiacSign];return ei?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",children:[G<=5&&G>0&&(0,s.jsxs)("div",{className:"bg-red-600/90 backdrop-blur-sm text-white px-4 py-2 text-center text-sm font-medium z-[200] relative",children:["⚠️ Your session will expire in ",G," minute",1!==G?"s":"",". Please scan your QR code again to continue."]}),(0,s.jsx)("header",{className:"md:hidden bg-black/20 backdrop-blur-sm border-b border-white/10 sticky top-0 z-[90]",children:(0,s.jsxs)("div",{className:"px-4 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"text-2xl flex-shrink-0",children:ei.symbol}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("h1",{className:"text-base font-bold text-white truncate",children:[_("welcome"),", ",e.name]}),(0,s.jsx)("p",{className:"text-gray-300 text-xs truncate",children:ei.name})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[(0,s.jsx)(v.A,{onLanguageChange:et}),(0,s.jsx)(k,{activeTab:M,onTabChange:e=>U(e),onLogout:er})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"flex bg-white/10 rounded-lg p-1 w-full max-w-sm",children:[(0,s.jsxs)("button",{onClick:()=>U("horoscope"),className:"flex items-center justify-center space-x-2 px-3 py-2 rounded-md transition-colors flex-1 ".concat("horoscope"===M?"bg-white/20 text-white shadow-sm":"text-gray-300 hover:text-white hover:bg-white/5"),children:[(0,s.jsx)(F.A,{size:16}),(0,s.jsx)("span",{className:"text-sm font-medium",children:_("horoscope")})]}),(0,s.jsxs)("button",{onClick:()=>U("guide"),className:"flex items-center justify-center space-x-2 px-3 py-2 rounded-md transition-colors flex-1 ".concat("guide"===M?"bg-white/20 text-white shadow-sm":"text-gray-300 hover:text-white hover:bg-white/5"),children:[(0,s.jsx)(C.A,{size:16}),(0,s.jsx)("span",{className:"text-sm font-medium",children:_("daily_guide")})]})]})})]})}),(0,s.jsx)("header",{className:"hidden md:block bg-black/20 backdrop-blur-sm border-b border-white/10 relative z-[90]",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"text-3xl",children:ei.symbol}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-xl font-bold text-white",children:[_("welcome"),", ",e.name]}),(0,s.jsxs)("p",{className:"text-gray-300 text-sm",children:[ei.name," • ",ei.dates]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(C.A,{size:16,className:"text-yellow-400"}),(0,s.jsxs)("span",{className:"font-medium ".concat(G<=5?"text-red-400":G<=10?"text-yellow-400":"text-green-400"),children:[G,"m"]})]}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(v.A,{onLanguageChange:et})}),(0,s.jsxs)("button",{onClick:er,className:"flex items-center space-x-2 text-gray-300 hover:text-red-400 transition-colors",title:_("logout"),children:[(0,s.jsx)(w.A,{size:20}),(0,s.jsx)("span",{className:"text-sm",children:_("logout")})]})]})]})})}),(0,s.jsx)("nav",{className:"hidden md:block bg-black/10 backdrop-blur-sm border-b border-white/10 relative z-[80]",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4",children:(0,s.jsx)("div",{className:"flex space-x-8",children:[{id:"horoscope",label:_("horoscope"),icon:F.A},{id:"guide",label:_("daily_guide"),icon:C.A}].map(e=>{let{id:a,label:t,icon:r}=e;return(0,s.jsxs)("button",{onClick:()=>U(a),className:"flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ".concat(M===a?"border-purple-400 text-white":"border-transparent text-gray-300 hover:text-white"),children:[(0,s.jsx)(r,{size:16}),(0,s.jsx)("span",{children:t})]},a)})})})}),(0,s.jsxs)("main",{className:"md:hidden px-3 py-4 pb-6 relative z-[10]",children:["horoscope"===M&&(0,s.jsxs)("div",{className:"space-y-4",children:[T.birthChart?(0,s.jsx)(B,{birthChart:T.birthChart}):(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center",children:[(0,s.jsx)(V.A,{className:"mx-auto mb-4 text-yellow-400",size:48}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:(0,s.jsx)(j,{text:"Your Birth Chart (Handahana) Awaits"})}),(0,s.jsx)("p",{className:"text-gray-300 text-sm mb-4",children:(0,s.jsx)(j,{text:"Get your personalized Vedic astrology birth chart with detailed interpretations based on your exact birth time and location."})}),(null==e?void 0:e.birthTime)&&(null==e?void 0:e.birthPlace)?(0,s.jsx)("button",{onClick:el,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg",children:(0,s.jsx)(j,{text:"Calculate My Birth Chart"})}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-yellow-300 text-sm mb-3",children:(0,s.jsx)(j,{text:"Birth time and place are required for accurate calculations."})}),(0,s.jsx)("p",{className:"text-gray-400 text-xs",children:(0,s.jsx)(j,{text:"Please contact admin to update your birth details."})})]})]}),T.personalHoroscopes&&T.personalHoroscopes.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-400 mb-4",children:(0,s.jsx)(j,{text:"Legacy Personal Messages"})})}),T.personalHoroscopes.map((e,a)=>(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10",children:[(0,s.jsxs)("h4",{className:"text-md font-semibold text-white mb-2 flex items-start",children:[(0,s.jsx)(F.A,{className:"mr-2 text-blue-400 flex-shrink-0 mt-0.5",size:16}),(0,s.jsx)("span",{className:"leading-tight",children:(0,s.jsx)(j,{text:e.title})})]}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:e.content})}),(0,s.jsx)("div",{className:"mt-2 text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]},e.id))]})]}),"guide"===M&&(T.dailyReading?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsxs)("h2",{className:"text-lg font-bold text-white mb-2 flex items-center",children:[(0,s.jsx)(C.A,{className:"mr-2 text-green-400 flex-shrink-0",size:18}),_("today_cosmic_guide")]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-1 text-sm text-gray-300",children:[(0,s.jsxs)("span",{children:["\uD83D\uDCC5 ",new Date(T.dailyReading.date).toLocaleDateString()]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(W.A,{className:"mr-1",size:14}),(0,s.jsx)(j,{text:T.dailyReading.mood,translations:es("mood"),fallback:T.dailyReading.mood})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsxs)("h3",{className:"text-base font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(V.A,{className:"mr-2 text-yellow-400 flex-shrink-0",size:16}),_("general_reading")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.generalReading,translations:es("generalReading"),fallback:T.dailyReading.generalReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsxs)("h3",{className:"text-base font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(H.A,{className:"mr-2 text-pink-400 flex-shrink-0",size:16}),_("love_relationships")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.loveReading,translations:es("loveReading"),fallback:T.dailyReading.loveReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsxs)("h3",{className:"text-base font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(J.A,{className:"mr-2 text-blue-400 flex-shrink-0",size:16}),_("career_money")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.careerReading,translations:es("careerReading"),fallback:T.dailyReading.careerReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsxs)("h3",{className:"text-base font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(q.A,{className:"mr-2 text-green-400 flex-shrink-0",size:16}),_("health_wellness")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.healthReading,translations:es("healthReading"),fallback:T.dailyReading.healthReading})})]})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:_("todays_lucky_elements")}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 text-center",children:[(0,s.jsx)(K.A,{className:"text-yellow-400 mx-auto mb-2",size:18}),(0,s.jsx)("h4",{className:"text-white font-semibold text-xs mb-1",children:_("lucky_number")}),(0,s.jsx)("p",{className:"text-xl font-bold text-yellow-400",children:T.dailyReading.luckyNumber})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 text-center",children:[(0,s.jsx)(Y.A,{className:"text-pink-400 mx-auto mb-2",size:18}),(0,s.jsx)("h4",{className:"text-white font-semibold text-xs mb-1",children:_("lucky_color")}),(0,s.jsx)("p",{className:"text-sm font-semibold text-pink-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyColor,translations:es("luckyColor"),fallback:T.dailyReading.luckyColor})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 text-center",children:[(0,s.jsx)(C.A,{className:"text-blue-400 mx-auto mb-2",size:18}),(0,s.jsx)("h4",{className:"text-white font-semibold text-xs mb-1",children:_("lucky_time")}),(0,s.jsx)("p",{className:"text-xs font-semibold text-blue-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyTime,translations:es("luckyTime"),fallback:T.dailyReading.luckyTime})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 text-center",children:[(0,s.jsx)(Q.A,{className:"text-purple-400 mx-auto mb-2",size:18}),(0,s.jsx)("h4",{className:"text-white font-semibold text-xs mb-1",children:_("lucky_gem")}),(0,s.jsx)("p",{className:"text-xs font-semibold text-purple-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyGem,translations:es("luckyGem"),fallback:T.dailyReading.luckyGem})})]})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 mb-3",children:[(0,s.jsxs)("h4",{className:"text-white font-semibold mb-2 flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 text-green-400",size:16}),_("daily_advice")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.advice,translations:es("advice"),fallback:T.dailyReading.advice})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-3",children:[(0,s.jsxs)("h4",{className:"text-white font-semibold mb-2 flex items-center",children:[(0,s.jsx)(Z.A,{className:"mr-2 text-orange-400",size:16}),_("compatible_signs_today")]}),(0,s.jsx)("p",{className:"text-orange-300 font-medium text-sm",children:(0,s.jsx)(j,{text:T.dailyReading.compatibility,translations:es("compatibility"),fallback:T.dailyReading.compatibility})})]})]})]}):(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center",children:[(0,s.jsx)(C.A,{className:"mx-auto mb-4 text-gray-400",size:40}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:_("no_daily_guide_available")}),(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:_("daily_guide_check_back")})]}))]}),(0,s.jsxs)("main",{className:"hidden md:block max-w-6xl mx-auto px-4 py-8 pb-8 relative z-[10]",style:{display:"block",visibility:"visible"},children:["horoscope"===M&&(0,s.jsxs)("div",{className:"space-y-6",style:{display:"block",visibility:"visible"},children:[T.birthChart?(0,s.jsx)("div",{style:{display:"block",visibility:"visible",width:"100%"},children:(0,s.jsx)(B,{birthChart:T.birthChart})}):(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center",children:[(0,s.jsx)(V.A,{className:"mx-auto mb-6 text-yellow-400",size:64}),(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white mb-4",children:(0,s.jsx)(j,{text:"Your Birth Chart (Handahana) Awaits"})}),(0,s.jsx)("p",{className:"text-gray-300 text-lg mb-6 max-w-2xl mx-auto",children:(0,s.jsx)(j,{text:"Discover your cosmic blueprint with a personalized Vedic astrology birth chart. Get detailed interpretations of your planetary positions, houses, and life guidance based on your exact birth time and location."})}),(null==e?void 0:e.birthTime)&&(null==e?void 0:e.birthPlace)?(0,s.jsx)("button",{onClick:el,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-200 shadow-lg text-lg",children:(0,s.jsx)(j,{text:"Calculate My Birth Chart"})}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-yellow-300 text-lg mb-4",children:(0,s.jsx)(j,{text:"Birth time and place are required for accurate calculations."})}),(0,s.jsx)("p",{className:"text-gray-400",children:(0,s.jsx)(j,{text:"Please contact admin to update your birth details."})})]})]}),T.personalHoroscopes&&T.personalHoroscopes.length>0&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-400 mb-6",children:(0,s.jsx)(j,{text:"Legacy Personal Messages"})})}),T.personalHoroscopes.map((e,a)=>(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10",children:[(0,s.jsxs)("h4",{className:"text-xl font-semibold text-white mb-4 flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 text-blue-400 flex-shrink-0",size:20}),(0,s.jsx)(j,{text:e.title})]}),(0,s.jsx)("p",{className:"text-gray-300 leading-relaxed text-lg",children:(0,s.jsx)(j,{text:e.content})}),(0,s.jsx)("div",{className:"mt-4 text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]},e.id))]})]}),"guide"===M&&(T.dailyReading?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-white mb-2 flex items-center",children:[(0,s.jsx)(C.A,{className:"mr-2 text-green-400 flex-shrink-0",size:20}),_("today_cosmic_guide")]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm text-gray-300",children:[(0,s.jsxs)("span",{children:["\uD83D\uDCC5 ",new Date(T.dailyReading.date).toLocaleDateString()]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(W.A,{className:"mr-1",size:16}),(0,s.jsx)(j,{text:T.dailyReading.mood,translations:es("mood"),fallback:T.dailyReading.mood})]})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(V.A,{className:"mr-2 text-yellow-400 flex-shrink-0",size:18}),_("general_reading")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-base",children:(0,s.jsx)(j,{text:T.dailyReading.generalReading,translations:es("generalReading"),fallback:T.dailyReading.generalReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(H.A,{className:"mr-2 text-pink-400 flex-shrink-0",size:18}),_("love_relationships")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-base",children:(0,s.jsx)(j,{text:T.dailyReading.loveReading,translations:es("loveReading"),fallback:T.dailyReading.loveReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(J.A,{className:"mr-2 text-blue-400 flex-shrink-0",size:18}),_("career_money")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-base",children:(0,s.jsx)(j,{text:T.dailyReading.careerReading,translations:es("careerReading"),fallback:T.dailyReading.careerReading})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-3 flex items-center",children:[(0,s.jsx)(q.A,{className:"mr-2 text-green-400",size:20}),_("health_wellness")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed text-base",children:(0,s.jsx)(j,{text:T.dailyReading.healthReading,translations:es("healthReading"),fallback:T.dailyReading.healthReading})})]})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:_("todays_lucky_elements")}),(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,s.jsx)(K.A,{className:"text-yellow-400 mx-auto mb-2",size:20}),(0,s.jsx)("h4",{className:"text-white font-semibold text-sm mb-1",children:_("lucky_number")}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:T.dailyReading.luckyNumber})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,s.jsx)(Y.A,{className:"text-pink-400 mx-auto mb-2",size:20}),(0,s.jsx)("h4",{className:"text-white font-semibold text-sm mb-1",children:_("lucky_color")}),(0,s.jsx)("p",{className:"text-lg font-semibold text-pink-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyColor,translations:es("luckyColor"),fallback:T.dailyReading.luckyColor})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,s.jsx)(C.A,{className:"text-blue-400 mx-auto mb-2",size:20}),(0,s.jsx)("h4",{className:"text-white font-semibold text-sm mb-1",children:_("lucky_time")}),(0,s.jsx)("p",{className:"text-sm font-semibold text-blue-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyTime,translations:es("luckyTime"),fallback:T.dailyReading.luckyTime})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 text-center",children:[(0,s.jsx)(Q.A,{className:"text-purple-400 mx-auto mb-2",size:24}),(0,s.jsx)("h4",{className:"text-white font-semibold text-sm mb-1",children:_("lucky_gem")}),(0,s.jsx)("p",{className:"text-sm font-semibold text-purple-400",children:(0,s.jsx)(j,{text:T.dailyReading.luckyGem,translations:es("luckyGem"),fallback:T.dailyReading.luckyGem})})]})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4 mb-4",children:[(0,s.jsxs)("h4",{className:"text-white font-semibold mb-3 flex items-center",children:[(0,s.jsx)(F.A,{className:"mr-2 text-green-400",size:20}),_("daily_advice")]}),(0,s.jsx)("p",{className:"text-gray-200 leading-relaxed",children:(0,s.jsx)(j,{text:T.dailyReading.advice,translations:es("advice"),fallback:T.dailyReading.advice})})]}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-lg p-4",children:[(0,s.jsxs)("h4",{className:"text-white font-semibold mb-3 flex items-center",children:[(0,s.jsx)(Z.A,{className:"mr-2 text-orange-400",size:20}),_("compatible_signs_today")]}),(0,s.jsx)("p",{className:"text-orange-300 font-medium",children:(0,s.jsx)(j,{text:T.dailyReading.compatibility,translations:es("compatibility"),fallback:T.dailyReading.compatibility})})]})]})]}):(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center",children:[(0,s.jsx)(C.A,{className:"mx-auto mb-4 text-gray-400",size:48}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:_("no_daily_guide_available")}),(0,s.jsx)("p",{className:"text-gray-300",children:_("daily_guide_check_back")})]}))]}),(0,s.jsx)(N.A,{})]}):(console.error("Zodiac info not found for sign:",e.zodiacSign),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:(0,s.jsx)(c.A,{title:_("configuration_error"),message:"Unable to load zodiac information. Please contact support.",onRetry:ea})}))}},28727:(e,a,t)=>{"use strict";t.d(a,{A:()=>c});var s=t(95155),r=t(12115),l=t(59377),i=t(51154),n=t(34869),o=t(5196),d=t(17122);function c(e){let{onLanguageChange:a,className:t=""}=e,{language:c,setLanguage:h,isTranslating:u}=(0,l.o)(),[x,m]=(0,r.useState)(!1),g=(0,r.useRef)(null),p=(0,r.useRef)(null),b=async e=>{e!==c&&(m(!1),a?a(e):h(e))};return(0,r.useEffect)(()=>{let e=e=>{g.current&&p.current&&!g.current.contains(e.target)&&!p.current.contains(e.target)&&m(!1)};if(x)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[x]),(0,s.jsxs)("div",{className:"relative inline-block ".concat(t),children:[(0,s.jsxs)("button",{ref:p,onClick:()=>m(!x),className:"flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-all duration-200 border border-white/20 hover:border-white/40 focus:outline-none focus:ring-2 focus:ring-purple-500/50",disabled:u,"aria-expanded":x,"aria-haspopup":"true",children:[u?(0,s.jsx)(i.A,{size:16,className:"animate-spin"}):(0,s.jsx)(n.A,{size:16}),(0,s.jsx)("span",{className:"text-sm font-medium",children:d.G[c]}),(0,s.jsx)("svg",{className:"w-4 h-4 transition-transform duration-200 ".concat(x?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),x&&(0,s.jsx)("div",{ref:g,className:"absolute top-full right-0 mt-2 bg-gray-900/95 backdrop-blur-md border border-white/30 rounded-lg shadow-2xl z-[100] min-w-[140px] overflow-hidden transition-all duration-200 ease-out opacity-100 scale-100",role:"menu","aria-orientation":"vertical",children:Object.entries(d.G).map(e=>{let[a,t]=e;return(0,s.jsxs)("button",{onClick:()=>b(a),className:"flex items-center justify-between w-full px-4 py-3 text-white hover:bg-white/20 transition-all duration-200 first:rounded-t-lg last:rounded-b-lg focus:outline-none focus:bg-white/20",role:"menuitem",children:[(0,s.jsx)("span",{className:"font-medium",children:t}),c===a&&(0,s.jsx)(o.A,{size:16,className:"text-green-400"})]},a)})})]})}},37831:(e,a,t)=>{"use strict";t.d(a,{Wh:()=>r,qK:()=>s});let s=["aries","taurus","gemini","cancer","leo","virgo","libra","scorpio","sagittarius","capricorn","aquarius","pisces"],r={aries:{name:"Aries",symbol:"♈",dates:"Mar 21 - Apr 19",element:"Fire"},taurus:{name:"Taurus",symbol:"♉",dates:"Apr 20 - May 20",element:"Earth"},gemini:{name:"Gemini",symbol:"♊",dates:"May 21 - Jun 20",element:"Air"},cancer:{name:"Cancer",symbol:"♋",dates:"Jun 21 - Jul 22",element:"Water"},leo:{name:"Leo",symbol:"♌",dates:"Jul 23 - Aug 22",element:"Fire"},virgo:{name:"Virgo",symbol:"♍",dates:"Aug 23 - Sep 22",element:"Earth"},libra:{name:"Libra",symbol:"♎",dates:"Sep 23 - Oct 22",element:"Air"},scorpio:{name:"Scorpio",symbol:"♏",dates:"Oct 23 - Nov 21",element:"Water"},sagittarius:{name:"Sagittarius",symbol:"♐",dates:"Nov 22 - Dec 21",element:"Fire"},capricorn:{name:"Capricorn",symbol:"♑",dates:"Dec 22 - Jan 19",element:"Earth"},aquarius:{name:"Aquarius",symbol:"♒",dates:"Jan 20 - Feb 18",element:"Air"},pisces:{name:"Pisces",symbol:"♓",dates:"Feb 19 - Mar 20",element:"Water"}}},69783:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var s=t(95155),r=t(85339),l=t(53904),i=t(35084);function n(e){let{title:a,message:t,onRetry:n,className:o=""}=e,{t:d}=(0,i.LL)(),c=a||d("something_went_wrong");return(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto p-6 ".concat(o),children:[(0,s.jsx)(r.A,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:c}),(0,s.jsx)("p",{className:"text-gray-300 mb-6",children:t}),n&&(0,s.jsxs)("button",{onClick:n,className:"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto",children:[(0,s.jsx)(l.A,{size:16}),d("try_again")]})]})}},91340:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var s=t(95155),r=t(12115),l=t(91788),i=t(54416);function n(){let[e,a]=(0,r.useState)(null),[t,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(window.matchMedia("(display-mode: standalone)").matches)return void d(!0);let e=e=>{e.preventDefault(),a(e),n(!0)},t=()=>{d(!0),n(!1),a(null)};return window.addEventListener("beforeinstallprompt",e),window.addEventListener("appinstalled",t),"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(e=>{console.log("Service Worker registered:",e)}).catch(e=>{console.error("Service Worker registration failed:",e)}),()=>{window.removeEventListener("beforeinstallprompt",e),window.removeEventListener("appinstalled",t)}},[]);let c=async()=>{if(e)try{await e.prompt();let{outcome:t}=await e.userChoice;"accepted"===t?console.log("User accepted the install prompt"):console.log("User dismissed the install prompt"),a(null),n(!1)}catch(e){console.error("Error during installation:",e)}},h=()=>{n(!1),sessionStorage.setItem("pwa-install-dismissed","true")};return o||!t||sessionStorage.getItem("pwa-install-dismissed")?null:(0,s.jsxs)("div",{className:"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 text-purple-400"}),(0,s.jsx)("h3",{className:"text-white font-semibold",children:"Install AstroConnect"})]}),(0,s.jsx)("button",{onClick:h,className:"text-gray-400 hover:text-white transition-colors",children:(0,s.jsx)(i.A,{size:20})})]}),(0,s.jsx)("p",{className:"text-gray-300 text-sm mb-4",children:"Install our app for quick access to your daily horoscope and cosmic insights!"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:c,className:"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Install"}),(0,s.jsx)("button",{onClick:h,className:"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors",children:"Not now"})]})]})}},92731:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var s=t(95155),r=t(51154),l=t(35084);function i(e){let{message:a,size:t="md",className:i=""}=e,{t:n}=(0,l.LL)(),o=a||n("loading_ellipsis");return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(i),children:[(0,s.jsx)(r.A,{className:"".concat({sm:"w-6 h-6",md:"w-12 h-12",lg:"w-16 h-16"}[t]," text-white animate-spin mb-4")}),o&&(0,s.jsx)("p",{className:"text-white text-center",children:o})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2812,9033,5084,8441,1684,7358],()=>a(24121)),_N_E=e.O()}]);
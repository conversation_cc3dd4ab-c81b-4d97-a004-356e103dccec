-- CreateEnum
CREATE TYPE "ZodiacSign" AS ENUM ('aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces');

-- CreateEnum
CREATE TYPE "LanguageCode" AS ENUM ('en', 'si');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "HoroscopeType" AS ENUM ('daily', 'weekly', 'monthly');

-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('admin', 'user');

-- CreateTable
CREATE TABLE "admins" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "role" "UserRole" NOT NULL DEFAULT 'admin',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_login" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "admins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "name" TEXT NOT NULL,
    "zodiac_sign" "ZodiacSign" NOT NULL,
    "birth_date" DATE NOT NULL,
    "qr_token" TEXT NOT NULL,
    "language_preference" "LanguageCode" NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "qr_code_mappings" (
    "id" TEXT NOT NULL,
    "qr_token" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_scanned" TIMESTAMP(3),
    "scan_count" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "qr_code_mappings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "horoscopes" (
    "id" TEXT NOT NULL,
    "zodiac_sign" "ZodiacSign" NOT NULL,
    "type" "HoroscopeType" NOT NULL,
    "content" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "language" "LanguageCode" NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "horoscopes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daily_guides" (
    "id" TEXT NOT NULL,
    "zodiac_sign" "ZodiacSign" NOT NULL,
    "date" DATE NOT NULL,
    "lucky_number" INTEGER NOT NULL,
    "lucky_color" TEXT NOT NULL,
    "lucky_time" TEXT NOT NULL,
    "advice" TEXT NOT NULL,
    "language" "LanguageCode" NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "daily_guides_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "translation_cache" (
    "id" TEXT NOT NULL,
    "original_text" TEXT NOT NULL,
    "translated_text" TEXT NOT NULL,
    "source_language" "LanguageCode" NOT NULL,
    "target_language" "LanguageCode" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "translation_cache_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "admins_email_key" ON "admins"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_qr_token_key" ON "users"("qr_token");

-- CreateIndex
CREATE UNIQUE INDEX "qr_code_mappings_qr_token_key" ON "qr_code_mappings"("qr_token");

-- CreateIndex
CREATE UNIQUE INDEX "horoscopes_zodiac_sign_type_date_language_key" ON "horoscopes"("zodiac_sign", "type", "date", "language");

-- CreateIndex
CREATE UNIQUE INDEX "daily_guides_zodiac_sign_date_language_key" ON "daily_guides"("zodiac_sign", "date", "language");

-- CreateIndex
CREATE UNIQUE INDEX "translation_cache_original_text_source_language_target_lang_key" ON "translation_cache"("original_text", "source_language", "target_language");

-- AddForeignKey
ALTER TABLE "qr_code_mappings" ADD CONSTRAINT "qr_code_mappings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

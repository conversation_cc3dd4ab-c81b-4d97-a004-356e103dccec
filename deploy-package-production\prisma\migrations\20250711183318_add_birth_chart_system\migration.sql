-- AlterTable
ALTER TABLE "users" ADD COLUMN     "birth_latitude" DOUBLE PRECISION,
ADD COLUMN     "birth_longitude" DOUBLE PRECISION,
ADD COLUMN     "birth_place" TEXT;

-- CreateTable
CREATE TABLE "birth_charts" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "birth_date_time" TIMESTAMP(3) NOT NULL,
    "birth_place" TEXT NOT NULL,
    "birth_latitude" DOUBLE PRECISION NOT NULL,
    "birth_longitude" DOUBLE PRECISION NOT NULL,
    "timezone" TEXT NOT NULL,
    "planet_positions" JSONB NOT NULL,
    "house_positions" JSONB NOT NULL,
    "aspects" JSONB NOT NULL,
    "nakshatras" JSONB NOT NULL,
    "dashas" JSONB NOT NULL,
    "ascendant" TEXT NOT NULL,
    "moon_sign" TEXT NOT NULL,
    "sun_sign" TEXT NOT NULL,
    "general_reading" TEXT,
    "strengths_weaknesses" TEXT,
    "career_guidance" TEXT,
    "relationship_guidance" TEXT,
    "health_guidance" TEXT,
    "readings_en" JSONB,
    "readings_si" JSONB,
    "calculated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "birth_charts_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "birth_charts_user_id_key" ON "birth_charts"("user_id");

-- AddForeignKey
ALTER TABLE "birth_charts" ADD CONSTRAINT "birth_charts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
